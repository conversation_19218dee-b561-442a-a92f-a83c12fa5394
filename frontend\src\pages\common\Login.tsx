import React from 'react';
import { Form, Input, Button, message, Card } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { login } from '@/api/auth';
import styles from './Login.module.less';

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const onFinish = async (values: LoginForm) => {
    try {
      const response = await login(values);
      const { access_token, refresh_token, device_id, user } = response;

      // 存储认证信息
      localStorage.setItem('token', access_token);
      localStorage.setItem('refreshToken', refresh_token);
      localStorage.setItem('deviceId', device_id);
      localStorage.setItem('user', JSON.stringify(user));

      message.success('登录成功');

      // 根据用户角色跳转到不同的页面
      switch (user.role) {
        case 'admin':
          navigate('/admin/dashboard');
          break;
        case 'teacher':
          navigate('/teacher/dashboard');
          break;
        case 'student':
          navigate('/student/dashboard');
          break;
        default:
          navigate('/');
      }
    } catch (error) {
      message.error('登录失败，请检查用户名和密码');
    }
  };

  // 使用测试账号
  const useTestAccount = () => {
    form.setFieldsValue({
      username: 'admin',
      password: '123456'
    });
    message.info('已填入测试账号：admin / 123456');
  };

  return (
    <div className={styles.container}>
      <Card title="量化积分管理系统" className={styles.loginCard}>
        <Form
          form={form}
          name="login"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          layout="vertical"
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
            />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              登录
            </Button>
          </Form.Item>

          <Form.Item>
            <Button
              type="default"
              onClick={useTestAccount}
              block
              style={{ marginTop: '8px' }}
            >
              使用测试账号 (admin/123456)
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login; 