import React from 'react';
import { Form, Input, Button, message, Card } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { login } from '@/api/auth';
import styles from './Login.module.less';

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const onFinish = async (values: LoginForm) => {
    try {
      const response = await login(values);
      const { access_token, refresh_token, device_id, user } = response;

      // 存储认证信息
      localStorage.setItem('token', access_token);
      localStorage.setItem('refreshToken', refresh_token);
      localStorage.setItem('deviceId', device_id);
      localStorage.setItem('user', JSON.stringify(user));

      message.success('登录成功');

      // 根据用户角色跳转到不同的页面
      switch (user.role) {
        case 'admin':
          navigate('/admin/dashboard');
          break;
        case 'teacher':
          navigate('/teacher/dashboard');
          break;
        case 'student':
          navigate('/student/dashboard');
          break;
        default:
          navigate('/');
      }
    } catch (error) {
      message.error('登录失败，请检查用户名和密码');
    }
  };

  // 使用测试账号直接登录
  const useTestAccount = async (role: 'admin' | 'teacher' | 'student' = 'admin') => {
    try {
      // 模拟测试账号登录，直接跳过后端验证
      const mockUsers = {
        admin: {
          id: 'test-admin-001',
          username: 'admin',
          role: 'admin',
          name: '测试管理员',
          studentId: undefined,
          classId: undefined,
          groupId: undefined,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        teacher: {
          id: 'test-teacher-001',
          username: 'teacher',
          role: 'teacher',
          name: '测试教师',
          studentId: undefined,
          classId: 'class-001',
          groupId: undefined,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        student: {
          id: 'test-student-001',
          username: 'student',
          role: 'student',
          name: '测试学生',
          studentId: 'stu-001',
          classId: 'class-001',
          groupId: 'group-001',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      };

      const mockUser = mockUsers[role];
      const mockTokens = {
        access_token: 'mock-access-token-' + Date.now(),
        refresh_token: 'mock-refresh-token-' + Date.now(),
        device_id: 'mock-device-' + Date.now()
      };

      // 存储认证信息
      localStorage.setItem('token', mockTokens.access_token);
      localStorage.setItem('refreshToken', mockTokens.refresh_token);
      localStorage.setItem('deviceId', mockTokens.device_id);
      localStorage.setItem('user', JSON.stringify(mockUser));

      message.success(`${mockUser.name}登录成功`);

      // 根据角色跳转到不同页面
      switch (role) {
        case 'admin':
          navigate('/admin/dashboard');
          break;
        case 'teacher':
          navigate('/teacher/dashboard');
          break;
        case 'student':
          navigate('/student/dashboard');
          break;
      }
    } catch (error) {
      message.error('测试登录失败');
    }
  };

  return (
    <div className={styles.container}>
      <Card title="量化积分管理系统" className={styles.loginCard}>
        <Form
          form={form}
          name="login"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          layout="vertical"
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
            />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              登录
            </Button>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <div style={{ display: 'flex', gap: '8px', marginTop: '8px' }}>
              <Button
                type="default"
                onClick={() => useTestAccount('admin')}
                style={{ flex: 1 }}
              >
                👑 管理员
              </Button>
              <Button
                type="default"
                onClick={() => useTestAccount('teacher')}
                style={{ flex: 1 }}
              >
                👨‍🏫 教师
              </Button>
              <Button
                type="default"
                onClick={() => useTestAccount('student')}
                style={{ flex: 1 }}
              >
                🎓 学生
              </Button>
            </div>
            <div style={{ textAlign: 'center', marginTop: '4px', fontSize: '12px', color: '#999' }}>
              测试账号快速登录
            </div>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login; 