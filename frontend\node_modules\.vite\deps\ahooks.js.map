{"version": 3, "sources": ["../../.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js", "../../.pnpm/lodash@4.17.21/node_modules/lodash/throttle.js", "../../.pnpm/react-fast-compare@3.2.2/node_modules/react-fast-compare/index.js", "../../.pnpm/screenfull@5.2.0/node_modules/screenfull/dist/screenfull.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/createUpdateEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useAntdTable/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useMemoizedFn/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/isDev.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/usePagination/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useUpdateEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useCreation/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/depsAreSame.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useUnmount/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useLatest/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/cache.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/cachePromise.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/cacheSubscribe.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useDebouncePlugin.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useLoadingDelayPlugin.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/isBrowser.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/subscribeReVisible.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/limit.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/isOnline.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/subscribeFocus.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useRetryPlugin.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useThrottlePlugin.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useMount/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useUpdate/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/Fetch.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/useRequestImplement.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/src/useRequest.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRequest/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useAsyncEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useBoolean/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useToggle/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/domTarget.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/getDocumentOrShadow.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/useEffectWithTarget.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/createEffectWithTarget.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useClickAway/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useControllableValue/index.js", "../../.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useCookieState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useCountDown/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useCounter/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDebounce/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/lodash-polyfill.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDebounceFn/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDebounceEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDeepCompareEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/createDeepCompareEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/depsEqual.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDeepCompareLayoutEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDocumentVisibility/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useEventListener/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDrag/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDrop/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useDynamicList/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useEventEmitter/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useEventTarget/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useExternal/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useFavicon/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useFocusWithin/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useFullscreen/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useFusionTable/fusionAdapter.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useFusionTable/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useGetState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useHistoryTravel/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useHover/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useInfiniteScroll/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/rect.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useInterval/index.js", "../../.pnpm/intersection-observer@0.12.2/node_modules/intersection-observer/intersection-observer.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useInViewport/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useIsomorphicLayoutEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/useDeepCompareWithTarget.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/isAppleDevice.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useKeyPress/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/createUseStorageState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useLocalStorageState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useLockFn/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useLongPress/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useMap/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRafState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useMouse/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useNetwork/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/usePrevious/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRafInterval/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useRafTimeout/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useReactive/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useResetState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useResponsive/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useSafeState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useUnmountedRef/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useScroll/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useSelections/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useSessionStorageState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useSet/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useSetState/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/useLayoutEffectWithTarget.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/utils/useIsomorphicLayoutEffectWithTarget.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useSize/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useTextSelection/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useThrottle/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useThrottleFn/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useThrottleEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useTimeout/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useTitle/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useTrackedEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useUpdateLayoutEffect/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useVirtualList/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useWebSocket/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useWhyDidYouUpdate/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useMutationObserver/index.js", "../../.pnpm/ahooks@3.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/ahooks/es/useTheme/index.js"], "sourcesContent": ["var isObject = require('./isObject'),\n    now = require('./now'),\n    toNumber = require('./toNumber');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nmodule.exports = debounce;\n", "var debounce = require('./debounce'),\n    isObject = require('./isObject');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a throttled function that only invokes `func` at most once per\n * every `wait` milliseconds. The throttled function comes with a `cancel`\n * method to cancel delayed `func` invocations and a `flush` method to\n * immediately invoke them. Provide `options` to indicate whether `func`\n * should be invoked on the leading and/or trailing edge of the `wait`\n * timeout. The `func` is invoked with the last arguments provided to the\n * throttled function. Subsequent calls to the throttled function return the\n * result of the last `func` invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the throttled function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [<PERSON>'s article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.throttle` and `_.debounce`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to throttle.\n * @param {number} [wait=0] The number of milliseconds to throttle invocations to.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=true]\n *  Specify invoking on the leading edge of the timeout.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new throttled function.\n * @example\n *\n * // Avoid excessively updating the position while scrolling.\n * jQuery(window).on('scroll', _.throttle(updatePosition, 100));\n *\n * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.\n * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });\n * jQuery(element).on('click', throttled);\n *\n * // Cancel the trailing throttled invocation.\n * jQuery(window).on('popstate', throttled.cancel);\n */\nfunction throttle(func, wait, options) {\n  var leading = true,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  if (isObject(options)) {\n    leading = 'leading' in options ? !!options.leading : leading;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n  return debounce(func, wait, {\n    'leading': leading,\n    'maxWait': wait,\n    'trailing': trailing\n  });\n}\n\nmodule.exports = throttle;\n", "/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && (a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n\n    if (hasSet && (a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (((error.message || '').match(/stack|recursion/i))) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n", "/*!\n* screenfull\n* v5.2.0 - 2021-11-03\n* (c) <PERSON>dre Sorhus; MIT License\n*/\n(function () {\n\t'use strict';\n\n\tvar document = typeof window !== 'undefined' && typeof window.document !== 'undefined' ? window.document : {};\n\tvar isCommonjs = typeof module !== 'undefined' && module.exports;\n\n\tvar fn = (function () {\n\t\tvar val;\n\n\t\tvar fnMap = [\n\t\t\t[\n\t\t\t\t'requestFullscreen',\n\t\t\t\t'exitFullscreen',\n\t\t\t\t'fullscreenElement',\n\t\t\t\t'fullscreenEnabled',\n\t\t\t\t'fullscreenchange',\n\t\t\t\t'fullscreenerror'\n\t\t\t],\n\t\t\t// New WebKit\n\t\t\t[\n\t\t\t\t'webkitRequestFullscreen',\n\t\t\t\t'webkitExitFullscreen',\n\t\t\t\t'webkitFullscreenElement',\n\t\t\t\t'webkitFullscreenEnabled',\n\t\t\t\t'webkitfullscreenchange',\n\t\t\t\t'webkitfullscreenerror'\n\n\t\t\t],\n\t\t\t// Old WebKit\n\t\t\t[\n\t\t\t\t'webkitRequestFullScreen',\n\t\t\t\t'webkitCancelFullScreen',\n\t\t\t\t'webkitCurrentFullScreenElement',\n\t\t\t\t'webkitCancelFullScreen',\n\t\t\t\t'webkitfullscreenchange',\n\t\t\t\t'webkitfullscreenerror'\n\n\t\t\t],\n\t\t\t[\n\t\t\t\t'mozRequestFullScreen',\n\t\t\t\t'mozCancelFullScreen',\n\t\t\t\t'mozFullScreenElement',\n\t\t\t\t'mozFullScreenEnabled',\n\t\t\t\t'mozfullscreenchange',\n\t\t\t\t'mozfullscreenerror'\n\t\t\t],\n\t\t\t[\n\t\t\t\t'msRequestFullscreen',\n\t\t\t\t'msExitFullscreen',\n\t\t\t\t'msFullscreenElement',\n\t\t\t\t'msFullscreenEnabled',\n\t\t\t\t'MSFullscreenChange',\n\t\t\t\t'MSFullscreenError'\n\t\t\t]\n\t\t];\n\n\t\tvar i = 0;\n\t\tvar l = fnMap.length;\n\t\tvar ret = {};\n\n\t\tfor (; i < l; i++) {\n\t\t\tval = fnMap[i];\n\t\t\tif (val && val[1] in document) {\n\t\t\t\tfor (i = 0; i < val.length; i++) {\n\t\t\t\t\tret[fnMap[0][i]] = val[i];\n\t\t\t\t}\n\t\t\t\treturn ret;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t})();\n\n\tvar eventNameMap = {\n\t\tchange: fn.fullscreenchange,\n\t\terror: fn.fullscreenerror\n\t};\n\n\tvar screenfull = {\n\t\trequest: function (element, options) {\n\t\t\treturn new Promise(function (resolve, reject) {\n\t\t\t\tvar onFullScreenEntered = function () {\n\t\t\t\t\tthis.off('change', onFullScreenEntered);\n\t\t\t\t\tresolve();\n\t\t\t\t}.bind(this);\n\n\t\t\t\tthis.on('change', onFullScreenEntered);\n\n\t\t\t\telement = element || document.documentElement;\n\n\t\t\t\tvar returnPromise = element[fn.requestFullscreen](options);\n\n\t\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\t\treturnPromise.then(onFullScreenEntered).catch(reject);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t},\n\t\texit: function () {\n\t\t\treturn new Promise(function (resolve, reject) {\n\t\t\t\tif (!this.isFullscreen) {\n\t\t\t\t\tresolve();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar onFullScreenExit = function () {\n\t\t\t\t\tthis.off('change', onFullScreenExit);\n\t\t\t\t\tresolve();\n\t\t\t\t}.bind(this);\n\n\t\t\t\tthis.on('change', onFullScreenExit);\n\n\t\t\t\tvar returnPromise = document[fn.exitFullscreen]();\n\n\t\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\t\treturnPromise.then(onFullScreenExit).catch(reject);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t},\n\t\ttoggle: function (element, options) {\n\t\t\treturn this.isFullscreen ? this.exit() : this.request(element, options);\n\t\t},\n\t\tonchange: function (callback) {\n\t\t\tthis.on('change', callback);\n\t\t},\n\t\tonerror: function (callback) {\n\t\t\tthis.on('error', callback);\n\t\t},\n\t\ton: function (event, callback) {\n\t\t\tvar eventName = eventNameMap[event];\n\t\t\tif (eventName) {\n\t\t\t\tdocument.addEventListener(eventName, callback, false);\n\t\t\t}\n\t\t},\n\t\toff: function (event, callback) {\n\t\t\tvar eventName = eventNameMap[event];\n\t\t\tif (eventName) {\n\t\t\t\tdocument.removeEventListener(eventName, callback, false);\n\t\t\t}\n\t\t},\n\t\traw: fn\n\t};\n\n\tif (!fn) {\n\t\tif (isCommonjs) {\n\t\t\tmodule.exports = {isEnabled: false};\n\t\t} else {\n\t\t\twindow.screenfull = {isEnabled: false};\n\t\t}\n\n\t\treturn;\n\t}\n\n\tObject.defineProperties(screenfull, {\n\t\tisFullscreen: {\n\t\t\tget: function () {\n\t\t\t\treturn Boolean(document[fn.fullscreenElement]);\n\t\t\t}\n\t\t},\n\t\telement: {\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\treturn document[fn.fullscreenElement];\n\t\t\t}\n\t\t},\n\t\tisEnabled: {\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\t// Coerce to boolean in case of old WebKit\n\t\t\t\treturn Boolean(document[fn.fullscreenEnabled]);\n\t\t\t}\n\t\t}\n\t});\n\n\tif (isCommonjs) {\n\t\tmodule.exports = screenfull;\n\t} else {\n\t\twindow.screenfull = screenfull;\n\t}\n})();\n", "import { useRef } from 'react';\nexport var createUpdateEffect = function (hook) {\n  return function (effect, deps) {\n    var isMounted = useRef(false);\n    // for react-refresh\n    hook(function () {\n      return function () {\n        isMounted.current = false;\n      };\n    }, []);\n    hook(function () {\n      if (!isMounted.current) {\n        isMounted.current = true;\n      } else {\n        return effect();\n      }\n    }, deps);\n  };\n};\nexport default createUpdateEffect;", "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport usePagination from '../usePagination';\nimport useUpdateEffect from '../useUpdateEffect';\nvar useAntdTable = function (service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var form = options.form,\n    _b = options.defaultType,\n    defaultType = _b === void 0 ? 'simple' : _b,\n    defaultParams = options.defaultParams,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    _d = options.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    _e = options.ready,\n    ready = _e === void 0 ? true : _e,\n    rest = __rest(options, [\"form\", \"defaultType\", \"defaultParams\", \"manual\", \"refreshDeps\", \"ready\"]);\n  var result = usePagination(service, __assign(__assign({\n    ready: ready,\n    manual: true\n  }, rest), {\n    onSuccess: function () {\n      var _a;\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runSuccessRef.current = true;\n      (_a = rest.onSuccess) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([rest], __read(args), false));\n    }\n  }));\n  var _f = result.params,\n    params = _f === void 0 ? [] : _f,\n    run = result.run;\n  var cacheFormTableData = params[2] || {};\n  var _g = __read(useState((cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.type) || defaultType), 2),\n    type = _g[0],\n    setType = _g[1];\n  var allFormDataRef = useRef({});\n  var defaultDataSourceRef = useRef([]);\n  var runSuccessRef = useRef(false);\n  var isAntdV4 = !!(form === null || form === void 0 ? void 0 : form.getInternalHooks);\n  // get current active field values\n  var getActiveFieldValues = function () {\n    if (!form) {\n      return {};\n    }\n    // antd 4\n    if (isAntdV4) {\n      return form.getFieldsValue(null, function () {\n        return true;\n      });\n    }\n    // antd 3\n    var allFieldsValue = form.getFieldsValue();\n    var activeFieldsValue = {};\n    Object.keys(allFieldsValue).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFieldsValue[key];\n      }\n    });\n    return activeFieldsValue;\n  };\n  var validateFields = function () {\n    if (!form) {\n      return Promise.resolve({});\n    }\n    var activeFieldsValue = getActiveFieldValues();\n    var fields = Object.keys(activeFieldsValue);\n    // antd 4\n    if (isAntdV4) {\n      return form.validateFields(fields);\n    }\n    // antd 3\n    return new Promise(function (resolve, reject) {\n      form.validateFields(fields, function (errors, values) {\n        if (errors) {\n          reject(errors);\n        } else {\n          resolve(values);\n        }\n      });\n    });\n  };\n  var restoreForm = function () {\n    if (!form) {\n      return;\n    }\n    // antd v4\n    if (isAntdV4) {\n      return form.setFieldsValue(allFormDataRef.current);\n    }\n    // antd v3\n    var activeFieldsValue = {};\n    Object.keys(allFormDataRef.current).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFormDataRef.current[key];\n      }\n    });\n    form.setFieldsValue(activeFieldsValue);\n  };\n  var changeType = function () {\n    var activeFieldsValue = getActiveFieldValues();\n    allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), activeFieldsValue);\n    setType(function (t) {\n      return t === 'simple' ? 'advance' : 'simple';\n    });\n  };\n  var _submit = function (initPagination) {\n    if (!ready) {\n      return;\n    }\n    setTimeout(function () {\n      validateFields().then(function (values) {\n        if (values === void 0) {\n          values = {};\n        }\n        var pagination = initPagination || __assign(__assign({\n          pageSize: options.defaultPageSize || 10\n        }, (params === null || params === void 0 ? void 0 : params[0]) || {}), {\n          current: 1\n        });\n        if (!form) {\n          // @ts-ignore\n          run(pagination);\n          return;\n        }\n        // record all form data\n        allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), values);\n        // @ts-ignore\n        run(pagination, values, {\n          allFormData: allFormDataRef.current,\n          type: type\n        });\n      }).catch(function (err) {\n        return err;\n      });\n    });\n  };\n  var reset = function () {\n    var _a, _b;\n    if (form) {\n      form.resetFields();\n    }\n    _submit(__assign(__assign({}, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}), {\n      pageSize: options.defaultPageSize || ((_b = (_a = options.defaultParams) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.pageSize) || 10,\n      current: 1\n    }));\n  };\n  var submit = function (e) {\n    var _a, _b, _c;\n    (_a = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);\n    _submit(runSuccessRef.current ? undefined : __assign({\n      pageSize: options.defaultPageSize || ((_c = (_b = options.defaultParams) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.pageSize) || 10,\n      current: 1\n    }, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}));\n  };\n  var onTableChange = function (pagination, filters, sorter, extra) {\n    var _a = __read(params || []),\n      oldPaginationParams = _a[0],\n      restParams = _a.slice(1);\n    run.apply(void 0, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: pagination.current,\n      pageSize: pagination.pageSize,\n      filters: filters,\n      sorter: sorter,\n      extra: extra\n    })], __read(restParams), false));\n  };\n  // init\n  useEffect(function () {\n    // if has cache, use cached params. ignore manual and ready.\n    if (params.length > 0) {\n      allFormDataRef.current = (cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.allFormData) || {};\n      restoreForm();\n      // @ts-ignore\n      run.apply(void 0, __spreadArray([], __read(params), false));\n      return;\n    }\n    if (ready) {\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      if (!manual) {\n        _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n      }\n    }\n  }, []);\n  // change search type, restore form data\n  useUpdateEffect(function () {\n    if (!ready) {\n      return;\n    }\n    restoreForm();\n  }, [type]);\n  // refresh & ready change on the same time\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      if (form) {\n        form.resetFields();\n      }\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!ready) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      result.pagination.changeCurrent(1);\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return __assign(__assign({}, result), {\n    tableProps: {\n      dataSource: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.list) || defaultDataSourceRef.current,\n      loading: result.loading,\n      onChange: useMemoizedFn(onTableChange),\n      pagination: {\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize,\n        total: result.pagination.total\n      }\n    },\n    search: {\n      submit: useMemoizedFn(submit),\n      type: type,\n      changeType: useMemoizedFn(changeType),\n      reset: useMemoizedFn(reset)\n    }\n  });\n};\nexport default useAntdTable;", "import { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useMemoizedFn(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMemoizedFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useRef(fn);\n  // why not write `fnRef.current = fn`?\n  // https://github.com/alibaba/hooks/issues/728\n  fnRef.current = useMemo(function () {\n    return fn;\n  }, [fn]);\n  var memoizedFn = useRef(undefined);\n  if (!memoizedFn.current) {\n    memoizedFn.current = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(this, args);\n    };\n  }\n  return memoizedFn.current;\n}\nexport default useMemoizedFn;", "export var isObject = function (value) {\n  return value !== null && typeof value === 'object';\n};\nexport var isFunction = function (value) {\n  return typeof value === 'function';\n};\nexport var isString = function (value) {\n  return typeof value === 'string';\n};\nexport var isBoolean = function (value) {\n  return typeof value === 'boolean';\n};\nexport var isNumber = function (value) {\n  return typeof value === 'number';\n};\nexport var isUndef = function (value) {\n  return typeof value === 'undefined';\n};", "var isDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';\nexport default isDev;", "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { useMemo } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nvar usePagination = function (service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var _b = options.defaultPageSize,\n    defaultPageSize = _b === void 0 ? 10 : _b,\n    _c = options.defaultCurrent,\n    defaultCurrent = _c === void 0 ? 1 : _c,\n    rest = __rest(options, [\"defaultPageSize\", \"defaultCurrent\"]);\n  var result = useRequest(service, __assign({\n    defaultParams: [{\n      current: defaultCurrent,\n      pageSize: defaultPageSize\n    }],\n    refreshDepsAction: function () {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      changeCurrent(1);\n    }\n  }, rest));\n  var _d = result.params[0] || {},\n    _e = _d.current,\n    current = _e === void 0 ? 1 : _e,\n    _f = _d.pageSize,\n    pageSize = _f === void 0 ? defaultPageSize : _f;\n  var total = ((_a = result.data) === null || _a === void 0 ? void 0 : _a.total) || 0;\n  var totalPage = useMemo(function () {\n    return Math.ceil(total / pageSize);\n  }, [pageSize, total]);\n  var onChange = function (c, p) {\n    var toCurrent = c <= 0 ? 1 : c;\n    var toPageSize = p <= 0 ? 1 : p;\n    var tempTotalPage = Math.ceil(total / toPageSize);\n    if (toCurrent > tempTotalPage) {\n      toCurrent = Math.max(1, tempTotalPage);\n    }\n    var _a = __read(result.params || []),\n      _b = _a[0],\n      oldPaginationParams = _b === void 0 ? {} : _b,\n      restParams = _a.slice(1);\n    result.run.apply(result, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: toCurrent,\n      pageSize: toPageSize\n    })], __read(restParams), false));\n  };\n  var changeCurrent = function (c) {\n    onChange(c, pageSize);\n  };\n  var changePageSize = function (p) {\n    onChange(current, p);\n  };\n  return __assign(__assign({}, result), {\n    pagination: {\n      current: current,\n      pageSize: pageSize,\n      total: total,\n      totalPage: totalPage,\n      onChange: useMemoizedFn(onChange),\n      changeCurrent: useMemoizedFn(changeCurrent),\n      changePageSize: useMemoizedFn(changePageSize)\n    }\n  });\n};\nexport default usePagination;", "import { __read, __spreadArray } from \"tslib\";\nimport { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\n// support refreshDeps & ready\nvar useAutoRunPlugin = function (fetchInstance, _a) {\n  var manual = _a.manual,\n    _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    _c = _a.defaultParams,\n    defaultParams = _c === void 0 ? [] : _c,\n    _d = _a.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    refreshDepsAction = _a.refreshDepsAction;\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(defaultParams), false));\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      if (refreshDepsAction) {\n        refreshDepsAction();\n      } else {\n        fetchInstance.refresh();\n      }\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return {\n    onBefore: function () {\n      if (!ready) {\n        return {\n          stopNow: true\n        };\n      }\n    }\n  };\n};\nuseAutoRunPlugin.onInit = function (_a) {\n  var _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    manual = _a.manual;\n  return {\n    loading: !manual && ready\n  };\n};\nexport default useAutoRunPlugin;", "import { useEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useEffect);", "import { __read, __spreadArray } from \"tslib\";\nimport { useRef } from 'react';\nimport useCreation from '../../../useCreation';\nimport useUnmount from '../../../useUnmount';\nimport { setCache, getCache } from '../utils/cache';\nimport { setCachePromise, getCachePromise } from '../utils/cachePromise';\nimport { trigger, subscribe } from '../utils/cacheSubscribe';\nvar useCachePlugin = function (fetchInstance, _a) {\n  var cacheKey = _a.cacheKey,\n    _b = _a.cacheTime,\n    cacheTime = _b === void 0 ? 5 * 60 * 1000 : _b,\n    _c = _a.staleTime,\n    staleTime = _c === void 0 ? 0 : _c,\n    customSetCache = _a.setCache,\n    customGetCache = _a.getCache;\n  var unSubscribeRef = useRef(undefined);\n  var currentPromiseRef = useRef(undefined);\n  var _setCache = function (key, cachedData) {\n    if (customSetCache) {\n      customSetCache(cachedData);\n    } else {\n      setCache(key, cacheTime, cachedData);\n    }\n    trigger(key, cachedData.data);\n  };\n  var _getCache = function (key, params) {\n    if (params === void 0) {\n      params = [];\n    }\n    if (customGetCache) {\n      return customGetCache(params);\n    }\n    return getCache(key);\n  };\n  useCreation(function () {\n    if (!cacheKey) {\n      return;\n    }\n    // get data from cache when init\n    var cacheData = _getCache(cacheKey);\n    if (cacheData && Object.hasOwnProperty.call(cacheData, 'data')) {\n      fetchInstance.state.data = cacheData.data;\n      fetchInstance.state.params = cacheData.params;\n      if (staleTime === -1 || Date.now() - cacheData.time <= staleTime) {\n        fetchInstance.state.loading = false;\n      }\n    }\n    // subscribe same cachekey update, trigger update\n    unSubscribeRef.current = subscribe(cacheKey, function (data) {\n      fetchInstance.setState({\n        data: data\n      });\n    });\n  }, []);\n  useUnmount(function () {\n    var _a;\n    (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n  });\n  if (!cacheKey) {\n    return {};\n  }\n  return {\n    onBefore: function (params) {\n      var cacheData = _getCache(cacheKey, params);\n      if (!cacheData || !Object.hasOwnProperty.call(cacheData, 'data')) {\n        return {};\n      }\n      // If the data is fresh, stop request\n      if (staleTime === -1 || Date.now() - cacheData.time <= staleTime) {\n        return {\n          loading: false,\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined,\n          returnNow: true\n        };\n      } else {\n        // If the data is stale, return data, and request continue\n        return {\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined\n        };\n      }\n    },\n    onRequest: function (service, args) {\n      var servicePromise = getCachePromise(cacheKey);\n      // If has servicePromise, and is not trigger by self, then use it\n      if (servicePromise && servicePromise !== currentPromiseRef.current) {\n        return {\n          servicePromise: servicePromise\n        };\n      }\n      servicePromise = service.apply(void 0, __spreadArray([], __read(args), false));\n      currentPromiseRef.current = servicePromise;\n      setCachePromise(cacheKey, servicePromise);\n      return {\n        servicePromise: servicePromise\n      };\n    },\n    onSuccess: function (data, params) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trgger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: params,\n          time: Date.now()\n        });\n        // resubscribe\n        unSubscribeRef.current = subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    },\n    onMutate: function (data) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trigger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: fetchInstance.state.params,\n          time: Date.now()\n        });\n        // resubscribe\n        unSubscribeRef.current = subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    }\n  };\n};\nexport default useCachePlugin;", "import { useRef } from 'react';\nimport depsAreSame from '../utils/depsAreSame';\nvar useCreation = function (factory, deps) {\n  var current = useRef({\n    deps: deps,\n    obj: undefined,\n    initialized: false\n  }).current;\n  if (current.initialized === false || !depsAreSame(current.deps, deps)) {\n    current.deps = deps;\n    current.obj = factory();\n    current.initialized = true;\n  }\n  return current.obj;\n};\nexport default useCreation;", "function depsAreSame(oldDeps, deps) {\n  if (oldDeps === deps) {\n    return true;\n  }\n  for (var i = 0; i < oldDeps.length; i++) {\n    if (!Object.is(oldDeps[i], deps[i])) {\n      return false;\n    }\n  }\n  return true;\n}\nexport default depsAreSame;", "import { useEffect } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useUnmount = function (fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useUnmount expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  useEffect(function () {\n    return function () {\n      fnRef.current();\n    };\n  }, []);\n};\nexport default useUnmount;", "import { useRef } from 'react';\nfunction useLatest(value) {\n  var ref = useRef(value);\n  ref.current = value;\n  return ref;\n}\nexport default useLatest;", "import { __assign } from \"tslib\";\nvar cache = new Map();\nvar setCache = function (key, cacheTime, cachedData) {\n  var currentCache = cache.get(key);\n  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {\n    clearTimeout(currentCache.timer);\n  }\n  var timer = undefined;\n  if (cacheTime > -1) {\n    // if cache out, clear it\n    timer = setTimeout(function () {\n      cache.delete(key);\n    }, cacheTime);\n  }\n  cache.set(key, __assign(__assign({}, cachedData), {\n    timer: timer\n  }));\n};\nvar getCache = function (key) {\n  return cache.get(key);\n};\nvar clearCache = function (key) {\n  if (key) {\n    var cacheKeys = Array.isArray(key) ? key : [key];\n    cacheKeys.forEach(function (cacheKey) {\n      return cache.delete(cacheKey);\n    });\n  } else {\n    cache.clear();\n  }\n};\nexport { getCache, setCache, clearCache };", "var cachePromise = new Map();\nvar getCachePromise = function (cacheKey) {\n  return cachePromise.get(cacheKey);\n};\nvar setCachePromise = function (cacheKey, promise) {\n  // Should cache the same promise, cannot be promise.finally\n  // Because the promise.finally will change the reference of the promise\n  cachePromise.set(cacheKey, promise);\n  // no use promise.finally for compatibility\n  promise.then(function (res) {\n    cachePromise.delete(cacheKey);\n    return res;\n  }).catch(function () {\n    cachePromise.delete(cacheKey);\n  });\n};\nexport { getCachePromise, setCachePromise };", "var listeners = {};\nvar trigger = function (key, data) {\n  if (listeners[key]) {\n    listeners[key].forEach(function (item) {\n      return item(data);\n    });\n  }\n};\nvar subscribe = function (key, listener) {\n  if (!listeners[key]) {\n    listeners[key] = [];\n  }\n  listeners[key].push(listener);\n  return function unsubscribe() {\n    var index = listeners[key].indexOf(listener);\n    listeners[key].splice(index, 1);\n  };\n};\nexport { trigger, subscribe };", "import { __read, __spreadArray } from \"tslib\";\nimport debounce from 'lodash/debounce';\nimport { useEffect, useMemo, useRef } from 'react';\nvar useDebouncePlugin = function (fetchInstance, _a) {\n  var debounceWait = _a.debounceWait,\n    debounceLeading = _a.debounceLeading,\n    debounceTrailing = _a.debounceTrailing,\n    debounceMaxWait = _a.debounceMaxWait;\n  var debouncedRef = useRef(undefined);\n  var options = useMemo(function () {\n    var ret = {};\n    if (debounceLeading !== undefined) {\n      ret.leading = debounceLeading;\n    }\n    if (debounceTrailing !== undefined) {\n      ret.trailing = debounceTrailing;\n    }\n    if (debounceMaxWait !== undefined) {\n      ret.maxWait = debounceMaxWait;\n    }\n    return ret;\n  }, [debounceLeading, debounceTrailing, debounceMaxWait]);\n  useEffect(function () {\n    if (debounceWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      debouncedRef.current = debounce(function (callback) {\n        callback();\n      }, debounceWait, options);\n      // debounce runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.call(debouncedRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        fetchInstance.runAsync = _originRunAsync_1;\n      };\n    }\n  }, [debounceWait, options]);\n  if (!debounceWait) {\n    return {};\n  }\n  return {\n    onCancel: function () {\n      var _a;\n      (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useDebouncePlugin;", "import { useRef } from 'react';\nvar useLoadingDelayPlugin = function (fetchInstance, _a) {\n  var loadingDelay = _a.loadingDelay,\n    ready = _a.ready;\n  var timerRef = useRef(undefined);\n  if (!loadingDelay) {\n    return {};\n  }\n  var cancelTimeout = function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  };\n  return {\n    onBefore: function () {\n      cancelTimeout();\n      // Two cases:\n      // 1. ready === undefined\n      // 2. ready === true\n      if (ready !== false) {\n        timerRef.current = setTimeout(function () {\n          fetchInstance.setState({\n            loading: true\n          });\n        }, loadingDelay);\n      }\n      return {\n        loading: false\n      };\n    },\n    onFinally: function () {\n      cancelTimeout();\n    },\n    onCancel: function () {\n      cancelTimeout();\n    }\n  };\n};\nexport default useLoadingDelayPlugin;", "import { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\nimport isDocumentVisible from '../utils/isDocumentVisible';\nimport subscribeReVisible from '../utils/subscribeReVisible';\nvar usePollingPlugin = function (fetchInstance, _a) {\n  var pollingInterval = _a.pollingInterval,\n    _b = _a.pollingWhenHidden,\n    pollingWhenHidden = _b === void 0 ? true : _b,\n    _c = _a.pollingErrorRetryCount,\n    pollingErrorRetryCount = _c === void 0 ? -1 : _c;\n  var timerRef = useRef(undefined);\n  var unsubscribeRef = useRef(undefined);\n  var countRef = useRef(0);\n  var stopPolling = function () {\n    var _a;\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useUpdateEffect(function () {\n    if (!pollingInterval) {\n      stopPolling();\n    }\n  }, [pollingInterval]);\n  if (!pollingInterval) {\n    return {};\n  }\n  return {\n    onBefore: function () {\n      stopPolling();\n    },\n    onError: function () {\n      countRef.current += 1;\n    },\n    onSuccess: function () {\n      countRef.current = 0;\n    },\n    onFinally: function () {\n      if (pollingErrorRetryCount === -1 ||\n      // When an error occurs, the request is not repeated after pollingErrorRetryCount retries\n      pollingErrorRetryCount !== -1 && countRef.current <= pollingErrorRetryCount) {\n        timerRef.current = setTimeout(function () {\n          // if pollingWhenHidden = false && document is hidden, then stop polling and subscribe revisible\n          if (!pollingWhenHidden && !isDocumentVisible()) {\n            unsubscribeRef.current = subscribeReVisible(function () {\n              fetchInstance.refresh();\n            });\n          } else {\n            fetchInstance.refresh();\n          }\n        }, pollingInterval);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function () {\n      stopPolling();\n    }\n  };\n};\nexport default usePollingPlugin;", "var isBrowser = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport default isBrowser;", "import isBrowser from '../../../utils/isBrowser';\nexport default function isDocumentVisible() {\n  if (isBrowser) {\n    return document.visibilityState !== 'hidden';\n  }\n  return true;\n}", "import isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    listeners.splice(index, 1);\n  };\n}\nif (isBrowser) {\n  var revalidate = function () {\n    if (!isDocumentVisible()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n}\nexport default subscribe;", "import { useEffect, useRef } from 'react';\nimport useUnmount from '../../../useUnmount';\nimport limit from '../utils/limit';\nimport subscribeFocus from '../utils/subscribeFocus';\nvar useRefreshOnWindowFocusPlugin = function (fetchInstance, _a) {\n  var refreshOnWindowFocus = _a.refreshOnWindowFocus,\n    _b = _a.focusTimespan,\n    focusTimespan = _b === void 0 ? 5000 : _b;\n  var unsubscribeRef = useRef(undefined);\n  var stopSubscribe = function () {\n    var _a;\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useEffect(function () {\n    if (refreshOnWindowFocus) {\n      var limitRefresh_1 = limit(fetchInstance.refresh.bind(fetchInstance), focusTimespan);\n      unsubscribeRef.current = subscribeFocus(function () {\n        limitRefresh_1();\n      });\n    }\n    return function () {\n      stopSubscribe();\n    };\n  }, [refreshOnWindowFocus, focusTimespan]);\n  useUnmount(function () {\n    stopSubscribe();\n  });\n  return {};\n};\nexport default useRefreshOnWindowFocusPlugin;", "import { __read, __spreadArray } from \"tslib\";\nexport default function limit(fn, timespan) {\n  var pending = false;\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (pending) return;\n    pending = true;\n    fn.apply(void 0, __spreadArray([], __read(args), false));\n    setTimeout(function () {\n      pending = false;\n    }, timespan);\n  };\n}", "import isBrowser from '../../../utils/isBrowser';\nvar isOnline = function () {\n  if (isBrowser && typeof navigator.onLine !== 'undefined') {\n    return navigator.onLine;\n  }\n  return true;\n};\nexport default isOnline;", "// from swr\nimport isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nimport isOnline from './isOnline';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  };\n}\nif (isBrowser) {\n  var revalidate = function () {\n    if (!isDocumentVisible() || !isOnline()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n  window.addEventListener('focus', revalidate, false);\n}\nexport default subscribe;", "import { useRef } from 'react';\nvar useRetryPlugin = function (fetchInstance, _a) {\n  var retryInterval = _a.retryInterval,\n    retryCount = _a.retryCount;\n  var timerRef = useRef(undefined);\n  var countRef = useRef(0);\n  var triggerByRetry = useRef(false);\n  if (!retryCount) {\n    return {};\n  }\n  return {\n    onBefore: function () {\n      if (!triggerByRetry.current) {\n        countRef.current = 0;\n      }\n      triggerByRetry.current = false;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    },\n    onSuccess: function () {\n      countRef.current = 0;\n    },\n    onError: function () {\n      countRef.current += 1;\n      if (retryCount === -1 || countRef.current <= retryCount) {\n        // Exponential backoff\n        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1000 * Math.pow(2, countRef.current), 30000);\n        timerRef.current = setTimeout(function () {\n          triggerByRetry.current = true;\n          fetchInstance.refresh();\n        }, timeout);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function () {\n      countRef.current = 0;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    }\n  };\n};\nexport default useRetryPlugin;", "import { __read, __spreadArray } from \"tslib\";\nimport throttle from 'lodash/throttle';\nimport { useEffect, useRef } from 'react';\nvar useThrottlePlugin = function (fetchInstance, _a) {\n  var throttleWait = _a.throttleWait,\n    throttleLeading = _a.throttleLeading,\n    throttleTrailing = _a.throttleTrailing;\n  var throttledRef = useRef(undefined);\n  var options = {};\n  if (throttleLeading !== undefined) {\n    options.leading = throttleLeading;\n  }\n  if (throttleTrailing !== undefined) {\n    options.trailing = throttleTrailing;\n  }\n  useEffect(function () {\n    if (throttleWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      throttledRef.current = throttle(function (callback) {\n        callback();\n      }, throttleWait, options);\n      // throttle runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.call(throttledRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        fetchInstance.runAsync = _originRunAsync_1;\n        (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n      };\n    }\n  }, [throttleWait, throttleLeading, throttleTrailing]);\n  if (!throttleWait) {\n    return {};\n  }\n  return {\n    onCancel: function () {\n      var _a;\n      (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useThrottlePlugin;", "import { useEffect } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useMount = function (fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMount: parameter `fn` expected to be a function, but got \\\"\".concat(typeof fn, \"\\\".\"));\n    }\n  }\n  useEffect(function () {\n    fn === null || fn === void 0 ? void 0 : fn();\n  }, []);\n};\nexport default useMount;", "import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nvar useUpdate = function () {\n  var _a = __read(useState({}), 2),\n    setState = _a[1];\n  return useCallback(function () {\n    return setState({});\n  }, []);\n};\nexport default useUpdate;", "import { __assign, __awaiter, __generator, __read, __rest, __spreadArray } from \"tslib\";\nimport { isFunction } from '../../utils';\nvar Fetch = /** @class */function () {\n  function Fetch(serviceRef, options, subscribe, initState) {\n    if (initState === void 0) {\n      initState = {};\n    }\n    this.serviceRef = serviceRef;\n    this.options = options;\n    this.subscribe = subscribe;\n    this.initState = initState;\n    this.count = 0;\n    this.state = {\n      loading: false,\n      params: undefined,\n      data: undefined,\n      error: undefined\n    };\n    this.state = __assign(__assign(__assign({}, this.state), {\n      loading: !options.manual\n    }), initState);\n  }\n  Fetch.prototype.setState = function (s) {\n    if (s === void 0) {\n      s = {};\n    }\n    this.state = __assign(__assign({}, this.state), s);\n    this.subscribe();\n  };\n  Fetch.prototype.runPluginHandler = function (event) {\n    var rest = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      rest[_i - 1] = arguments[_i];\n    }\n    // @ts-ignore\n    var r = this.pluginImpls.map(function (i) {\n      var _a;\n      return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([i], __read(rest), false));\n    }).filter(Boolean);\n    return Object.assign.apply(Object, __spreadArray([{}], __read(r), false));\n  };\n  Fetch.prototype.runAsync = function () {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var currentCount, _a, _b, stopNow, _c, returnNow, state, servicePromise, res, error_1;\n      var _d;\n      var _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;\n      return __generator(this, function (_q) {\n        switch (_q.label) {\n          case 0:\n            this.count += 1;\n            currentCount = this.count;\n            _a = this.runPluginHandler('onBefore', params), _b = _a.stopNow, stopNow = _b === void 0 ? false : _b, _c = _a.returnNow, returnNow = _c === void 0 ? false : _c, state = __rest(_a, [\"stopNow\", \"returnNow\"]);\n            // stop request\n            if (stopNow) {\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState(__assign({\n              loading: true,\n              params: params\n            }, state));\n            // return now\n            if (returnNow) {\n              return [2 /*return*/, Promise.resolve(state.data)];\n            }\n            (_f = (_e = this.options).onBefore) === null || _f === void 0 ? void 0 : _f.call(_e, params);\n            _q.label = 1;\n          case 1:\n            _q.trys.push([1, 3,, 4]);\n            servicePromise = this.runPluginHandler('onRequest', this.serviceRef.current, params).servicePromise;\n            if (!servicePromise) {\n              servicePromise = (_d = this.serviceRef).current.apply(_d, __spreadArray([], __read(params), false));\n            }\n            return [4 /*yield*/, servicePromise];\n          case 2:\n            res = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            // const formattedResult = this.options.formatResultRef.current ? this.options.formatResultRef.current(res) : res;\n            this.setState({\n              data: res,\n              error: undefined,\n              loading: false\n            });\n            (_h = (_g = this.options).onSuccess) === null || _h === void 0 ? void 0 : _h.call(_g, res, params);\n            this.runPluginHandler('onSuccess', res, params);\n            (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, res, undefined);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, res, undefined);\n            }\n            return [2 /*return*/, res];\n          case 3:\n            error_1 = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState({\n              error: error_1,\n              loading: false\n            });\n            (_m = (_l = this.options).onError) === null || _m === void 0 ? void 0 : _m.call(_l, error_1, params);\n            this.runPluginHandler('onError', error_1, params);\n            (_p = (_o = this.options).onFinally) === null || _p === void 0 ? void 0 : _p.call(_o, params, undefined, error_1);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, undefined, error_1);\n            }\n            throw error_1;\n          case 4:\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  Fetch.prototype.run = function () {\n    var _this = this;\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    this.runAsync.apply(this, __spreadArray([], __read(params), false)).catch(function (error) {\n      if (!_this.options.onError) {\n        console.error(error);\n      }\n    });\n  };\n  Fetch.prototype.cancel = function () {\n    this.count += 1;\n    this.setState({\n      loading: false\n    });\n    this.runPluginHandler('onCancel');\n  };\n  Fetch.prototype.refresh = function () {\n    // @ts-ignore\n    this.run.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.refreshAsync = function () {\n    // @ts-ignore\n    return this.runAsync.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.mutate = function (data) {\n    var targetData = isFunction(data) ? data(this.state.data) : data;\n    this.runPluginHandler('onMutate', targetData);\n    this.setState({\n      data: targetData\n    });\n  };\n  return Fetch;\n}();\nexport default Fetch;", "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport useCreation from '../../useCreation';\nimport useLatest from '../../useLatest';\nimport useMemoizedFn from '../../useMemoizedFn';\nimport useMount from '../../useMount';\nimport useUnmount from '../../useUnmount';\nimport useUpdate from '../../useUpdate';\nimport isDev from '../../utils/isDev';\nimport Fetch from './Fetch';\nfunction useRequestImplement(service, options, plugins) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (plugins === void 0) {\n    plugins = [];\n  }\n  var _a = options.manual,\n    manual = _a === void 0 ? false : _a,\n    _b = options.ready,\n    ready = _b === void 0 ? true : _b,\n    rest = __rest(options, [\"manual\", \"ready\"]);\n  if (isDev) {\n    if (options.defaultParams && !Array.isArray(options.defaultParams)) {\n      console.warn(\"expected defaultParams is array, got \".concat(typeof options.defaultParams));\n    }\n  }\n  var fetchOptions = __assign({\n    manual: manual,\n    ready: ready\n  }, rest);\n  var serviceRef = useLatest(service);\n  var update = useUpdate();\n  var fetchInstance = useCreation(function () {\n    var initState = plugins.map(function (p) {\n      var _a;\n      return (_a = p === null || p === void 0 ? void 0 : p.onInit) === null || _a === void 0 ? void 0 : _a.call(p, fetchOptions);\n    }).filter(Boolean);\n    return new Fetch(serviceRef, fetchOptions, update, Object.assign.apply(Object, __spreadArray([{}], __read(initState), false)));\n  }, []);\n  fetchInstance.options = fetchOptions;\n  // run all plugins hooks\n  fetchInstance.pluginImpls = plugins.map(function (p) {\n    return p(fetchInstance, fetchOptions);\n  });\n  useMount(function () {\n    if (!manual && ready) {\n      // useCachePlugin can set fetchInstance.state.params from cache when init\n      var params = fetchInstance.state.params || options.defaultParams || [];\n      // @ts-ignore\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(params), false));\n    }\n  });\n  useUnmount(function () {\n    fetchInstance.cancel();\n  });\n  return {\n    loading: fetchInstance.state.loading,\n    data: fetchInstance.state.data,\n    error: fetchInstance.state.error,\n    params: fetchInstance.state.params || [],\n    cancel: useMemoizedFn(fetchInstance.cancel.bind(fetchInstance)),\n    refresh: useMemoizedFn(fetchInstance.refresh.bind(fetchInstance)),\n    refreshAsync: useMemoizedFn(fetchInstance.refreshAsync.bind(fetchInstance)),\n    run: useMemoizedFn(fetchInstance.run.bind(fetchInstance)),\n    runAsync: useMemoizedFn(fetchInstance.runAsync.bind(fetchInstance)),\n    mutate: useMemoizedFn(fetchInstance.mutate.bind(fetchInstance))\n  };\n}\nexport default useRequestImplement;", "import { __read, __spreadArray } from \"tslib\";\nimport useAutoRunPlugin from './plugins/useAutoRunPlugin';\nimport useCachePlugin from './plugins/useCachePlugin';\nimport useDebouncePlugin from './plugins/useDebouncePlugin';\nimport useLoadingDelayPlugin from './plugins/useLoadingDelayPlugin';\nimport usePollingPlugin from './plugins/usePollingPlugin';\nimport useRefreshOnWindowFocusPlugin from './plugins/useRefreshOnWindowFocusPlugin';\nimport useRetryPlugin from './plugins/useRetryPlugin';\nimport useThrottlePlugin from './plugins/useThrottlePlugin';\nimport useRequestImplement from './useRequestImplement';\n// function useRequest<TData, TParams extends any[], TFormated, TTFormated extends TFormated = any>(\n//   service: Service<TData, TParams>,\n//   options: OptionsWithFormat<TData, TParams, TFormated, TTFormated>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TFormated, TParams>\n// function useRequest<TData, TParams extends any[]>(\n//   service: Service<TData, TParams>,\n//   options?: OptionsWithoutFormat<TData, TParams>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TData, TParams>\nfunction useRequest(service, options, plugins) {\n  return useRequestImplement(service, options, __spreadArray(__spreadArray([], __read(plugins || []), false), [useDebouncePlugin, useLoadingDelayPlugin, usePollingPlugin, useRefreshOnWindowFocusPlugin, useThrottlePlugin, useAutoRunPlugin, useCachePlugin, useRetryPlugin], false));\n}\nexport default useRequest;", "import useRequest from './src/useRequest';\nimport { clearCache } from './src/utils/cache';\nexport { clearCache };\nexport default useRequest;", "import { __awaiter, __generator } from \"tslib\";\nimport { useEffect } from 'react';\nimport { isFunction } from '../utils';\nfunction isAsyncGenerator(val) {\n  return isFunction(val[Symbol.asyncIterator]);\n}\nfunction useAsyncEffect(effect, deps) {\n  useEffect(function () {\n    var e = effect();\n    var cancelled = false;\n    function execute() {\n      return __awaiter(this, void 0, void 0, function () {\n        var result;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              if (!isAsyncGenerator(e)) return [3 /*break*/, 4];\n              _a.label = 1;\n            case 1:\n              if (!true) return [3 /*break*/, 3];\n              return [4 /*yield*/, e.next()];\n            case 2:\n              result = _a.sent();\n              if (result.done || cancelled) {\n                return [3 /*break*/, 3];\n              }\n              return [3 /*break*/, 1];\n            case 3:\n              return [3 /*break*/, 6];\n            case 4:\n              return [4 /*yield*/, e];\n            case 5:\n              _a.sent();\n              _a.label = 6;\n            case 6:\n              return [2 /*return*/];\n          }\n        });\n      });\n    }\n    execute();\n    return function () {\n      cancelled = true;\n    };\n  }, deps);\n}\nexport default useAsyncEffect;", "import { __read } from \"tslib\";\nimport { useMemo } from 'react';\nimport useToggle from '../useToggle';\nexport default function useBoolean(defaultValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useToggle(!!defaultValue), 2),\n    state = _a[0],\n    _b = _a[1],\n    toggle = _b.toggle,\n    set = _b.set;\n  var actions = useMemo(function () {\n    var setTrue = function () {\n      return set(true);\n    };\n    var setFalse = function () {\n      return set(false);\n    };\n    return {\n      toggle: toggle,\n      set: function (v) {\n        return set(!!v);\n      },\n      setTrue: setTrue,\n      setFalse: setFalse\n    };\n  }, []);\n  return [state, actions];\n}", "import { __read } from \"tslib\";\nimport { useMemo, useState } from 'react';\nfunction useToggle(defaultValue, reverseValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useState(defaultValue), 2),\n    state = _a[0],\n    setState = _a[1];\n  var actions = useMemo(function () {\n    var reverseValueOrigin = reverseValue === undefined ? !defaultValue : reverseValue;\n    var toggle = function () {\n      return setState(function (s) {\n        return s === defaultValue ? reverseValueOrigin : defaultValue;\n      });\n    };\n    var set = function (value) {\n      return setState(value);\n    };\n    var setLeft = function () {\n      return setState(defaultValue);\n    };\n    var setRight = function () {\n      return setState(reverseValueOrigin);\n    };\n    return {\n      toggle: toggle,\n      set: set,\n      setLeft: setLeft,\n      setRight: setRight\n    };\n    // useToggle ignore value change\n    // }, [defaultValue, reverseValue]);\n  }, []);\n  return [state, actions];\n}\nexport default useToggle;", "import { isFunction } from './index';\nimport isBrowser from './isBrowser';\nexport function getTargetElement(target, defaultElement) {\n  if (!isBrowser) {\n    return undefined;\n  }\n  if (!target) {\n    return defaultElement;\n  }\n  var targetElement;\n  if (isFunction(target)) {\n    targetElement = target();\n  } else if ('current' in target) {\n    targetElement = target.current;\n  } else {\n    targetElement = target;\n  }\n  return targetElement;\n}", "import { getTargetElement } from '../utils/domTarget';\nvar checkIfAllInShadow = function (targets) {\n  return targets.every(function (item) {\n    var targetElement = getTargetElement(item);\n    if (!targetElement) {\n      return false;\n    }\n    if (targetElement.getRootNode() instanceof ShadowRoot) {\n      return true;\n    }\n    return false;\n  });\n};\nvar getShadow = function (node) {\n  if (!node) {\n    return document;\n  }\n  return node.getRootNode();\n};\nvar getDocumentOrShadow = function (target) {\n  if (!target || !document.getRootNode) {\n    return document;\n  }\n  var targets = Array.isArray(target) ? target : [target];\n  if (checkIfAllInShadow(targets)) {\n    return getShadow(getTargetElement(targets[0]));\n  }\n  return document;\n};\nexport default getDocumentOrShadow;", "import { useEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useEffect);\nexport default useEffectWithTarget;", "import { useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport depsAreSame from './depsAreSame';\nimport { getTargetElement } from './domTarget';\nvar createEffectWithTarget = function (useEffectType) {\n  /**\n   *\n   * @param effect\n   * @param deps\n   * @param target target should compare ref.current vs ref.current, dom vs dom, ()=>dom vs ()=>dom\n   */\n  var useEffectWithTarget = function (effect, deps, target) {\n    var hasInitRef = useRef(false);\n    var lastElementRef = useRef([]);\n    var lastDepsRef = useRef([]);\n    var unLoadRef = useRef(undefined);\n    useEffectType(function () {\n      var _a;\n      var targets = Array.isArray(target) ? target : [target];\n      var els = targets.map(function (item) {\n        return getTargetElement(item);\n      });\n      // init run\n      if (!hasInitRef.current) {\n        hasInitRef.current = true;\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n        return;\n      }\n      if (els.length !== lastElementRef.current.length || !depsAreSame(lastElementRef.current, els) || !depsAreSame(lastDepsRef.current, deps)) {\n        (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n      }\n    });\n    useUnmount(function () {\n      var _a;\n      (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n      // for react-refresh\n      hasInitRef.current = false;\n    });\n  };\n  return useEffectWithTarget;\n};\nexport default createEffectWithTarget;", "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport getDocumentOrShadow from '../utils/getDocumentOrShadow';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nexport default function useClickAway(onClickAway, target, eventName) {\n  if (eventName === void 0) {\n    eventName = 'click';\n  }\n  var onClickAwayRef = useLatest(onClickAway);\n  useEffectWithTarget(function () {\n    var handler = function (event) {\n      var targets = Array.isArray(target) ? target : [target];\n      if (targets.some(function (item) {\n        var targetElement = getTargetElement(item);\n        return !targetElement || targetElement.contains(event.target);\n      })) {\n        return;\n      }\n      onClickAwayRef.current(event);\n    };\n    var documentOrShadow = getDocumentOrShadow(target);\n    var eventNames = Array.isArray(eventName) ? eventName : [eventName];\n    eventNames.forEach(function (event) {\n      return documentOrShadow.addEventListener(event, handler);\n    });\n    return function () {\n      eventNames.forEach(function (event) {\n        return documentOrShadow.removeEventListener(event, handler);\n      });\n    };\n  }, Array.isArray(eventName) ? eventName : [eventName], target);\n}", "import { __read, __spreadArray } from \"tslib\";\nimport { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdate from '../useUpdate';\nfunction useControllableValue(defaultProps, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var props = defaultProps !== null && defaultProps !== void 0 ? defaultProps : {};\n  var defaultValue = options.defaultValue,\n    _a = options.defaultValuePropName,\n    defaultValuePropName = _a === void 0 ? 'defaultValue' : _a,\n    _b = options.valuePropName,\n    valuePropName = _b === void 0 ? 'value' : _b,\n    _c = options.trigger,\n    trigger = _c === void 0 ? 'onChange' : _c;\n  var value = props[valuePropName];\n  var isControlled = Object.prototype.hasOwnProperty.call(props, valuePropName);\n  var initialValue = useMemo(function () {\n    if (isControlled) {\n      return value;\n    }\n    if (Object.prototype.hasOwnProperty.call(props, defaultValuePropName)) {\n      return props[defaultValuePropName];\n    }\n    return defaultValue;\n  }, []);\n  var stateRef = useRef(initialValue);\n  if (isControlled) {\n    stateRef.current = value;\n  }\n  var update = useUpdate();\n  function setState(v) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    var r = isFunction(v) ? v(stateRef.current) : v;\n    if (!isControlled) {\n      stateRef.current = r;\n      update();\n    }\n    if (props[trigger]) {\n      props[trigger].apply(props, __spreadArray([r], __read(args), false));\n    }\n  }\n  return [stateRef.current, useMemoizedFn(setState)];\n}\nexport default useControllableValue;", "/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    name = encodeURIComponent(name)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      name + '=' + converter.write(value, name) + stringifiedAttributes)\n  }\n\n  function get (name) {\n    if (typeof document === 'undefined' || (arguments.length && !name)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n\n        if (name === found) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return name ? jar[name] : jar\n  }\n\n  return Object.create(\n    {\n      set,\n      get,\n      remove: function (name, attributes) {\n        set(\n          name,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\nexport { api as default };\n", "import { __assign, __read, __rest } from \"tslib\";\nimport Cookies from 'js-cookie';\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nfunction useCookieState(cookieKey, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = __read(useState(function () {\n      var cookieValue = Cookies.get(cookieKey);\n      if (isString(cookieValue)) {\n        return cookieValue;\n      }\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  var updateState = useMemoizedFn(function (newValue, newOptions) {\n    if (newOptions === void 0) {\n      newOptions = {};\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    var _a = __assign(__assign({}, options), newOptions),\n      defaultValue = _a.defaultValue,\n      restOptions = __rest(_a, [\"defaultValue\"]);\n    var value = isFunction(newValue) ? newValue(state) : newValue;\n    setState(value);\n    if (value === undefined) {\n      Cookies.remove(cookieKey);\n    } else {\n      Cookies.set(cookieKey, value, restOptions);\n    }\n  });\n  return [state, updateState];\n}\nexport default useCookieState;", "import { __read } from \"tslib\";\nimport dayjs from 'dayjs';\nimport { useEffect, useMemo, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils/index';\nvar calcLeft = function (target) {\n  if (!target) {\n    return 0;\n  }\n  // https://stackoverflow.com/questions/4310953/invalid-date-in-safari\n  var left = dayjs(target).valueOf() - Date.now();\n  return left < 0 ? 0 : left;\n};\nvar parseMs = function (milliseconds) {\n  return {\n    days: Math.floor(milliseconds / 86400000),\n    hours: Math.floor(milliseconds / 3600000) % 24,\n    minutes: Math.floor(milliseconds / 60000) % 60,\n    seconds: Math.floor(milliseconds / 1000) % 60,\n    milliseconds: Math.floor(milliseconds) % 1000\n  };\n};\nvar useCountdown = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options || {},\n    leftTime = _a.leftTime,\n    targetDate = _a.targetDate,\n    _b = _a.interval,\n    interval = _b === void 0 ? 1000 : _b,\n    onEnd = _a.onEnd;\n  var memoLeftTime = useMemo(function () {\n    return isNumber(leftTime) && leftTime > 0 ? Date.now() + leftTime : undefined;\n  }, [leftTime]);\n  var target = 'leftTime' in options ? memoLeftTime : targetDate;\n  var _c = __read(useState(function () {\n      return calcLeft(target);\n    }), 2),\n    timeLeft = _c[0],\n    setTimeLeft = _c[1];\n  var onEndRef = useLatest(onEnd);\n  useEffect(function () {\n    if (!target) {\n      // for stop\n      setTimeLeft(0);\n      return;\n    }\n    // 立即执行一次\n    setTimeLeft(calcLeft(target));\n    var timer = setInterval(function () {\n      var _a;\n      var targetLeft = calcLeft(target);\n      setTimeLeft(targetLeft);\n      if (targetLeft === 0) {\n        clearInterval(timer);\n        (_a = onEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onEndRef);\n      }\n    }, interval);\n    return function () {\n      return clearInterval(timer);\n    };\n  }, [target, interval]);\n  var formattedRes = useMemo(function () {\n    return parseMs(timeLeft);\n  }, [timeLeft]);\n  return [timeLeft, formattedRes];\n};\nexport default useCountdown;", "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nfunction getTargetValue(val, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var target = val;\n  if (isNumber(max)) {\n    target = Math.min(max, target);\n  }\n  if (isNumber(min)) {\n    target = Math.max(min, target);\n  }\n  return target;\n}\nfunction useCounter(initialValue, options) {\n  if (initialValue === void 0) {\n    initialValue = 0;\n  }\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var _a = __read(useState(function () {\n      return getTargetValue(initialValue, {\n        min: min,\n        max: max\n      });\n    }), 2),\n    current = _a[0],\n    setCurrent = _a[1];\n  var setValue = function (value) {\n    setCurrent(function (c) {\n      var target = isNumber(value) ? value : value(c);\n      return getTargetValue(target, {\n        max: max,\n        min: min\n      });\n    });\n  };\n  var inc = function (delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c + delta;\n    });\n  };\n  var dec = function (delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c - delta;\n    });\n  };\n  var set = function (value) {\n    setValue(value);\n  };\n  var reset = function () {\n    setValue(initialValue);\n  };\n  return [current, {\n    inc: useMemoizedFn(inc),\n    dec: useMemoizedFn(dec),\n    set: useMemoizedFn(set),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useCounter;", "import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useDebounceFn from '../useDebounceFn';\nfunction useDebounce(value, options) {\n  var _a = __read(useState(value), 2),\n    debounced = _a[0],\n    setDebounced = _a[1];\n  var run = useDebounceFn(function () {\n    setDebounced(value);\n  }, options).run;\n  useEffect(function () {\n    run();\n  }, [value]);\n  return debounced;\n}\nexport default useDebounce;", "import debounce from 'lodash/debounce';\nfunction isNodeOrWeb() {\n  var freeGlobal = (typeof global === 'undefined' ? 'undefined' : typeof global) == 'object' && global && global.Object === Object && global;\n  var freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n  return freeGlobal || freeSelf;\n}\nif (!isNodeOrWeb()) {\n  global.Date = Date;\n}\nexport { debounce };", "import { __read, __spreadArray } from \"tslib\";\nimport { debounce } from '../utils/lodash-polyfill';\nimport { useMemo } from 'react';\nimport useLatest from '../useLatest';\nimport useUnmount from '../useUnmount';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useDebounceFn(fn, options) {\n  var _a;\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useDebounceFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;\n  var debounced = useMemo(function () {\n    return debounce(function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));\n    }, wait, options);\n  }, []);\n  useUnmount(function () {\n    debounced.cancel();\n  });\n  return {\n    run: debounced,\n    cancel: debounced.cancel,\n    flush: debounced.flush\n  };\n}\nexport default useDebounceFn;", "import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useDebounceFn from '../useDebounceFn';\nimport useUpdateEffect from '../useUpdateEffect';\nfunction useDebounceEffect(effect, deps, options) {\n  var _a = __read(useState({}), 2),\n    flag = _a[0],\n    setFlag = _a[1];\n  var run = useDebounceFn(function () {\n    setFlag({});\n  }, options).run;\n  useEffect(function () {\n    return run();\n  }, deps);\n  useUpdateEffect(effect, [flag]);\n}\nexport default useDebounceEffect;", "import { useEffect } from 'react';\nimport { createDeepCompareEffect } from '../createDeepCompareEffect';\nexport default createDeepCompareEffect(useEffect);", "import { useRef } from 'react';\nimport { depsEqual } from '../utils/depsEqual';\nexport var createDeepCompareEffect = function (hook) {\n  return function (effect, deps) {\n    var ref = useRef(undefined);\n    var signalRef = useRef(0);\n    if (deps === undefined || !depsEqual(deps, ref.current)) {\n      signalRef.current += 1;\n    }\n    ref.current = deps;\n    hook(effect, [signalRef.current]);\n  };\n};", "import isEqual from 'react-fast-compare';\nexport var depsEqual = function (aDeps, bDeps) {\n  if (aDeps === void 0) {\n    aDeps = [];\n  }\n  if (bDeps === void 0) {\n    bDeps = [];\n  }\n  return isEqual(aDeps, bDeps);\n};", "import { useLayoutEffect } from 'react';\nimport { createDeepCompareEffect } from '../createDeepCompareEffect';\nexport default createDeepCompareEffect(useLayoutEffect);", "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport isBrowser from '../utils/isBrowser';\nvar getVisibility = function () {\n  if (!isBrowser) {\n    return 'visible';\n  }\n  return document.visibilityState;\n};\nfunction useDocumentVisibility() {\n  var _a = __read(useState(getVisibility), 2),\n    documentVisibility = _a[0],\n    setDocumentVisibility = _a[1];\n  useEventListener('visibilitychange', function () {\n    setDocumentVisibility(getVisibility());\n  }, {\n    target: function () {\n      return document;\n    }\n  });\n  return documentVisibility;\n}\nexport default useDocumentVisibility;", "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useEventListener(eventName, handler, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.enable,\n    enable = _a === void 0 ? true : _a;\n  var handlerRef = useLatest(handler);\n  useEffectWithTarget(function () {\n    if (!enable) {\n      return;\n    }\n    var targetElement = getTargetElement(options.target, window);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var eventListener = function (event) {\n      return handlerRef.current(event);\n    };\n    var eventNameArray = Array.isArray(eventName) ? eventName : [eventName];\n    eventNameArray.forEach(function (event) {\n      targetElement.addEventListener(event, eventListener, {\n        capture: options.capture,\n        once: options.once,\n        passive: options.passive\n      });\n    });\n    return function () {\n      eventNameArray.forEach(function (event) {\n        targetElement.removeEventListener(event, eventListener, {\n          capture: options.capture\n        });\n      });\n    };\n  }, [eventName, options.capture, options.once, options.passive, enable], options.target);\n}\nexport default useEventListener;", "import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport useMount from '../useMount';\nimport { isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar useDrag = function (data, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  var dataRef = useLatest(data);\n  var imageElementRef = useRef(undefined);\n  var dragImage = optionsRef.current.dragImage;\n  useMount(function () {\n    if (dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) {\n      var image = dragImage.image;\n      if (isString(image)) {\n        var imageElement = new Image();\n        imageElement.src = image;\n        imageElementRef.current = imageElement;\n      } else {\n        imageElementRef.current = image;\n      }\n    }\n  });\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onDragStart = function (event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      event.dataTransfer.setData('custom', JSON.stringify(dataRef.current));\n      if ((dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) && imageElementRef.current) {\n        var _c = dragImage.offsetX,\n          offsetX = _c === void 0 ? 0 : _c,\n          _d = dragImage.offsetY,\n          offsetY = _d === void 0 ? 0 : _d;\n        event.dataTransfer.setDragImage(imageElementRef.current, offsetX, offsetY);\n      }\n    };\n    var onDragEnd = function (event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.setAttribute('draggable', 'true');\n    targetElement.addEventListener('dragstart', onDragStart);\n    targetElement.addEventListener('dragend', onDragEnd);\n    return function () {\n      targetElement.removeEventListener('dragstart', onDragStart);\n      targetElement.removeEventListener('dragend', onDragEnd);\n    };\n  }, [], target);\n};\nexport default useDrag;", "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nimport { useRef } from 'react';\nvar useDrop = function (target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  // https://stackoverflow.com/a/26459269\n  var dragEnterTarget = useRef(undefined);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onData = function (dataTransfer, event) {\n      var uri = dataTransfer.getData('text/uri-list');\n      var dom = dataTransfer.getData('custom');\n      if (dom && optionsRef.current.onDom) {\n        var data = dom;\n        try {\n          data = JSON.parse(dom);\n        } catch (_a) {\n          data = dom;\n        }\n        optionsRef.current.onDom(data, event);\n        return;\n      }\n      if (uri && optionsRef.current.onUri) {\n        optionsRef.current.onUri(uri, event);\n        return;\n      }\n      if (dataTransfer.files && dataTransfer.files.length && optionsRef.current.onFiles) {\n        optionsRef.current.onFiles(Array.from(dataTransfer.files), event);\n        return;\n      }\n      if (dataTransfer.items && dataTransfer.items.length && optionsRef.current.onText) {\n        dataTransfer.items[0].getAsString(function (text) {\n          optionsRef.current.onText(text, event);\n        });\n      }\n    };\n    var onDragEnter = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      event.stopPropagation();\n      dragEnterTarget.current = event.target;\n      (_b = (_a = optionsRef.current).onDragEnter) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragOver = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      (_b = (_a = optionsRef.current).onDragOver) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragLeave = function (event) {\n      var _a, _b;\n      if (event.target === dragEnterTarget.current) {\n        (_b = (_a = optionsRef.current).onDragLeave) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      }\n    };\n    var onDrop = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      onData(event.dataTransfer, event);\n      (_b = (_a = optionsRef.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onPaste = function (event) {\n      var _a, _b;\n      onData(event.clipboardData, event);\n      (_b = (_a = optionsRef.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.addEventListener('dragenter', onDragEnter);\n    targetElement.addEventListener('dragover', onDragOver);\n    targetElement.addEventListener('dragleave', onDragLeave);\n    targetElement.addEventListener('drop', onDrop);\n    targetElement.addEventListener('paste', onPaste);\n    return function () {\n      targetElement.removeEventListener('dragenter', onDragEnter);\n      targetElement.removeEventListener('dragover', onDragOver);\n      targetElement.removeEventListener('dragleave', onDragLeave);\n      targetElement.removeEventListener('drop', onDrop);\n      targetElement.removeEventListener('paste', onPaste);\n    };\n  }, [], target);\n};\nexport default useDrop;", "import { __read, __spreadArray } from \"tslib\";\nimport { useCallback, useRef, useState } from 'react';\nimport isDev from '../utils/isDev';\nvar useDynamicList = function (initialList) {\n  if (initialList === void 0) {\n    initialList = [];\n  }\n  var counterRef = useRef(-1);\n  var keyList = useRef([]);\n  var setKey = useCallback(function (index) {\n    counterRef.current += 1;\n    keyList.current.splice(index, 0, counterRef.current);\n  }, []);\n  var _a = __read(useState(function () {\n      initialList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return initialList;\n    }), 2),\n    list = _a[0],\n    setList = _a[1];\n  var resetList = useCallback(function (newList) {\n    keyList.current = [];\n    setList(function () {\n      newList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return newList;\n    });\n  }, []);\n  var insert = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp.splice(index, 0, item);\n      setKey(index);\n      return temp;\n    });\n  }, []);\n  var getKey = useCallback(function (index) {\n    return keyList.current[index];\n  }, []);\n  var getIndex = useCallback(function (key) {\n    return keyList.current.findIndex(function (ele) {\n      return ele === key;\n    });\n  }, []);\n  var merge = useCallback(function (index, items) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      items.forEach(function (_, i) {\n        setKey(index + i);\n      });\n      temp.splice.apply(temp, __spreadArray([index, 0], __read(items), false));\n      return temp;\n    });\n  }, []);\n  var replace = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp[index] = item;\n      return temp;\n    });\n  }, []);\n  var remove = useCallback(function (index) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp.splice(index, 1);\n      // remove keys if necessary\n      try {\n        keyList.current.splice(index, 1);\n      } catch (e) {\n        console.error(e);\n      }\n      return temp;\n    });\n  }, []);\n  var batchRemove = useCallback(function (indexes) {\n    if (!Array.isArray(indexes)) {\n      if (isDev) {\n        console.error(\"`indexes` parameter of `batchRemove` function expected to be an array, but got \\\"\".concat(typeof indexes, \"\\\".\"));\n      }\n      return;\n    }\n    if (!indexes.length) {\n      return;\n    }\n    setList(function (prevList) {\n      var newKeyList = [];\n      var newList = prevList.filter(function (item, index) {\n        var shouldKeep = !indexes.includes(index);\n        if (shouldKeep) {\n          newKeyList.push(getKey(index));\n        }\n        return shouldKeep;\n      });\n      keyList.current = newKeyList;\n      return newList;\n    });\n  }, []);\n  var move = useCallback(function (oldIndex, newIndex) {\n    if (oldIndex === newIndex) {\n      return;\n    }\n    setList(function (l) {\n      var newList = __spreadArray([], __read(l), false);\n      var temp = newList.filter(function (_, index) {\n        return index !== oldIndex;\n      });\n      temp.splice(newIndex, 0, newList[oldIndex]);\n      // move keys if necessary\n      try {\n        var keyTemp = keyList.current.filter(function (_, index) {\n          return index !== oldIndex;\n        });\n        keyTemp.splice(newIndex, 0, keyList.current[oldIndex]);\n        keyList.current = keyTemp;\n      } catch (e) {\n        console.error(e);\n      }\n      return temp;\n    });\n  }, []);\n  var push = useCallback(function (item) {\n    setList(function (l) {\n      setKey(l.length);\n      return l.concat([item]);\n    });\n  }, []);\n  var pop = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(0, keyList.current.length - 1);\n    } catch (e) {\n      console.error(e);\n    }\n    setList(function (l) {\n      return l.slice(0, l.length - 1);\n    });\n  }, []);\n  var unshift = useCallback(function (item) {\n    setList(function (l) {\n      setKey(0);\n      return [item].concat(l);\n    });\n  }, []);\n  var shift = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(1, keyList.current.length);\n    } catch (e) {\n      console.error(e);\n    }\n    setList(function (l) {\n      return l.slice(1, l.length);\n    });\n  }, []);\n  var sortList = useCallback(function (result) {\n    return result.map(function (item, index) {\n      return {\n        key: index,\n        item: item\n      };\n    }) // add index into obj\n    .sort(function (a, b) {\n      return getIndex(a.key) - getIndex(b.key);\n    }) // sort based on the index of table\n    .filter(function (item) {\n      return !!item.item;\n    }) // remove undefined(s)\n    .map(function (item) {\n      return item.item;\n    });\n  },\n  // retrive the data\n  []);\n  return {\n    list: list,\n    insert: insert,\n    merge: merge,\n    replace: replace,\n    remove: remove,\n    batchRemove: batchRemove,\n    getKey: getKey,\n    getIndex: getIndex,\n    move: move,\n    push: push,\n    pop: pop,\n    unshift: unshift,\n    shift: shift,\n    sortList: sortList,\n    resetList: resetList\n  };\n};\nexport default useDynamicList;", "import { __values } from \"tslib\";\nimport { useRef, useEffect } from 'react';\nvar EventEmitter = /** @class */function () {\n  function EventEmitter() {\n    var _this = this;\n    this.subscriptions = new Set();\n    this.emit = function (val) {\n      var e_1, _a;\n      try {\n        for (var _b = __values(_this.subscriptions), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var subscription = _c.value;\n          subscription(val);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    };\n    this.useSubscription = function (callback) {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      var callbackRef = useRef(undefined);\n      callbackRef.current = callback;\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useEffect(function () {\n        function subscription(val) {\n          if (callbackRef.current) {\n            callbackRef.current(val);\n          }\n        }\n        _this.subscriptions.add(subscription);\n        return function () {\n          _this.subscriptions.delete(subscription);\n        };\n      }, []);\n    };\n  }\n  return EventEmitter;\n}();\nexport { EventEmitter };\nfunction useEventEmitter() {\n  var ref = useRef(undefined);\n  if (!ref.current) {\n    ref.current = new EventEmitter();\n  }\n  return ref.current;\n}\nexport default useEventEmitter;", "import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nfunction useEventTarget(options) {\n  var _a = options || {},\n    initialValue = _a.initialValue,\n    transformer = _a.transformer;\n  var _b = __read(useState(initialValue), 2),\n    value = _b[0],\n    setValue = _b[1];\n  var transformerRef = useLatest(transformer);\n  var reset = useCallback(function () {\n    return setValue(initialValue);\n  }, []);\n  var onChange = useCallback(function (e) {\n    var _value = e.target.value;\n    if (isFunction(transformerRef.current)) {\n      return setValue(transformerRef.current(_value));\n    }\n    // no transformer => U and T should be the same\n    return setValue(_value);\n  }, []);\n  return [value, {\n    onChange: onChange,\n    reset: reset\n  }];\n}\nexport default useEventTarget;", "import { __read } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\n// {[path]: count}\n// remove external when no used\nvar EXTERNAL_USED_COUNT = {};\nvar loadScript = function (path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var script = document.querySelector(\"script[src=\\\"\".concat(path, \"\\\"]\"));\n  if (!script) {\n    var newScript_1 = document.createElement('script');\n    newScript_1.src = path;\n    Object.keys(props).forEach(function (key) {\n      newScript_1[key] = props[key];\n    });\n    newScript_1.setAttribute('data-status', 'loading');\n    document.body.appendChild(newScript_1);\n    return {\n      ref: newScript_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: script,\n    status: script.getAttribute('data-status') || 'ready'\n  };\n};\nvar loadCss = function (path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var css = document.querySelector(\"link[href=\\\"\".concat(path, \"\\\"]\"));\n  if (!css) {\n    var newCss_1 = document.createElement('link');\n    newCss_1.rel = 'stylesheet';\n    newCss_1.href = path;\n    Object.keys(props).forEach(function (key) {\n      newCss_1[key] = props[key];\n    });\n    // IE9+\n    var isLegacyIECss = 'hideFocus' in newCss_1;\n    // use preload in IE Edge (to detect load errors)\n    if (isLegacyIECss && newCss_1.relList) {\n      newCss_1.rel = 'preload';\n      newCss_1.as = 'style';\n    }\n    newCss_1.setAttribute('data-status', 'loading');\n    document.head.appendChild(newCss_1);\n    return {\n      ref: newCss_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: css,\n    status: css.getAttribute('data-status') || 'ready'\n  };\n};\nvar useExternal = function (path, options) {\n  var _a = __read(useState(path ? 'loading' : 'unset'), 2),\n    status = _a[0],\n    setStatus = _a[1];\n  var ref = useRef(undefined);\n  useEffect(function () {\n    if (!path) {\n      setStatus('unset');\n      return;\n    }\n    var pathname = path.replace(/[|#].*$/, '');\n    if ((options === null || options === void 0 ? void 0 : options.type) === 'css' || !(options === null || options === void 0 ? void 0 : options.type) && /(^css!|\\.css$)/.test(pathname)) {\n      var result = loadCss(path, options === null || options === void 0 ? void 0 : options.css);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else if ((options === null || options === void 0 ? void 0 : options.type) === 'js' || !(options === null || options === void 0 ? void 0 : options.type) && /(^js!|\\.js$)/.test(pathname)) {\n      var result = loadScript(path, options === null || options === void 0 ? void 0 : options.js);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else {\n      // do nothing\n      console.error(\"Cannot infer the type of external resource, and please provide a type ('js' | 'css'). \" + 'Refer to the https://ahooks.js.org/hooks/dom/use-external/#options');\n    }\n    if (!ref.current) {\n      return;\n    }\n    if (EXTERNAL_USED_COUNT[path] === undefined) {\n      EXTERNAL_USED_COUNT[path] = 1;\n    } else {\n      EXTERNAL_USED_COUNT[path] += 1;\n    }\n    var handler = function (event) {\n      var _a;\n      var targetStatus = event.type === 'load' ? 'ready' : 'error';\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.setAttribute('data-status', targetStatus);\n      setStatus(targetStatus);\n    };\n    ref.current.addEventListener('load', handler);\n    ref.current.addEventListener('error', handler);\n    return function () {\n      var _a, _b, _c;\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener('load', handler);\n      (_b = ref.current) === null || _b === void 0 ? void 0 : _b.removeEventListener('error', handler);\n      EXTERNAL_USED_COUNT[path] -= 1;\n      if (EXTERNAL_USED_COUNT[path] === 0 && !(options === null || options === void 0 ? void 0 : options.keepWhenUnused)) {\n        (_c = ref.current) === null || _c === void 0 ? void 0 : _c.remove();\n      }\n      ref.current = undefined;\n    };\n  }, [path]);\n  return status;\n};\nexport default useExternal;", "import { useEffect } from 'react';\nvar ImgTypeMap = {\n  SVG: 'image/svg+xml',\n  ICO: 'image/x-icon',\n  GIF: 'image/gif',\n  PNG: 'image/png'\n};\nvar useFavicon = function (href) {\n  useEffect(function () {\n    if (!href) {\n      return;\n    }\n    var cutUrl = href.split('.');\n    var imgSuffix = cutUrl[cutUrl.length - 1].toLocaleUpperCase();\n    var link = document.querySelector(\"link[rel*='icon']\") || document.createElement('link');\n    link.type = ImgTypeMap[imgSuffix];\n    link.href = href;\n    link.rel = 'shortcut icon';\n    document.getElementsByTagName('head')[0].appendChild(link);\n  }, [href]);\n};\nexport default useFavicon;", "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nexport default function useFocusWithin(target, options) {\n  var _a = __read(useState(false), 2),\n    isFocusWithin = _a[0],\n    setIsFocusWithin = _a[1];\n  var _b = options || {},\n    onFocus = _b.onFocus,\n    onBlur = _b.onBlur,\n    onChange = _b.onChange;\n  useEventListener('focusin', function (e) {\n    if (!isFocusWithin) {\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(true);\n      setIsFocusWithin(true);\n    }\n  }, {\n    target: target\n  });\n  useEventListener('focusout', function (e) {\n    var _a, _b;\n    if (isFocusWithin && !((_b = (_a = e.currentTarget) === null || _a === void 0 ? void 0 : _a.contains) === null || _b === void 0 ? void 0 : _b.call(_a, e.relatedTarget))) {\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(false);\n      setIsFocusWithin(false);\n    }\n  }, {\n    target: target\n  });\n  return isFocusWithin;\n}", "import { __read } from \"tslib\";\nimport { useEffect, useState, useRef } from 'react';\nimport screenfull from 'screenfull';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isBoolean } from '../utils';\nvar useFullscreen = function (target, options) {\n  var _a = options || {},\n    onExit = _a.onExit,\n    onEnter = _a.onEnter,\n    _b = _a.pageFullscreen,\n    pageFullscreen = _b === void 0 ? false : _b;\n  var _c = isBoolean(pageFullscreen) || !pageFullscreen ? {} : pageFullscreen,\n    _d = _c.className,\n    className = _d === void 0 ? 'ahooks-page-fullscreen' : _d,\n    _e = _c.zIndex,\n    zIndex = _e === void 0 ? 999999 : _e;\n  var onExitRef = useLatest(onExit);\n  var onEnterRef = useLatest(onEnter);\n  // The state of full screen may be changed by other scripts/components,\n  // so the initial value needs to be computed dynamically.\n  var _f = __read(useState(getIsFullscreen), 2),\n    state = _f[0],\n    setState = _f[1];\n  var stateRef = useRef(getIsFullscreen());\n  function getIsFullscreen() {\n    return screenfull.isEnabled && !!screenfull.element && screenfull.element === getTargetElement(target);\n  }\n  var invokeCallback = function (fullscreen) {\n    var _a, _b;\n    if (fullscreen) {\n      (_a = onEnterRef.current) === null || _a === void 0 ? void 0 : _a.call(onEnterRef);\n    } else {\n      (_b = onExitRef.current) === null || _b === void 0 ? void 0 : _b.call(onExitRef);\n    }\n  };\n  var updateFullscreenState = function (fullscreen) {\n    // Prevent repeated calls when the state is not changed.\n    if (stateRef.current !== fullscreen) {\n      invokeCallback(fullscreen);\n      setState(fullscreen);\n      stateRef.current = fullscreen;\n    }\n  };\n  var onScreenfullChange = function () {\n    var fullscreen = getIsFullscreen();\n    updateFullscreenState(fullscreen);\n  };\n  var togglePageFullscreen = function (fullscreen) {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var styleElem = document.getElementById(className);\n    if (fullscreen) {\n      el.classList.add(className);\n      if (!styleElem) {\n        styleElem = document.createElement('style');\n        styleElem.setAttribute('id', className);\n        styleElem.textContent = \"\\n          .\".concat(className, \" {\\n            position: fixed; left: 0; top: 0; right: 0; bottom: 0;\\n            width: 100% !important; height: 100% !important;\\n            z-index: \").concat(zIndex, \";\\n          }\");\n        el.appendChild(styleElem);\n      }\n    } else {\n      el.classList.remove(className);\n      if (styleElem) {\n        styleElem.remove();\n      }\n    }\n    updateFullscreenState(fullscreen);\n  };\n  var enterFullscreen = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (pageFullscreen) {\n      togglePageFullscreen(true);\n      return;\n    }\n    if (screenfull.isEnabled) {\n      try {\n        screenfull.request(el);\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n  var exitFullscreen = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (pageFullscreen) {\n      togglePageFullscreen(false);\n      return;\n    }\n    if (screenfull.isEnabled && screenfull.element === el) {\n      screenfull.exit();\n    }\n  };\n  var toggleFullscreen = function () {\n    if (state) {\n      exitFullscreen();\n    } else {\n      enterFullscreen();\n    }\n  };\n  useEffect(function () {\n    if (!screenfull.isEnabled || pageFullscreen) {\n      return;\n    }\n    screenfull.on('change', onScreenfullChange);\n    return function () {\n      screenfull.off('change', onScreenfullChange);\n    };\n  }, []);\n  return [state, {\n    enterFullscreen: useMemoizedFn(enterFullscreen),\n    exitFullscreen: useMemoizedFn(exitFullscreen),\n    toggleFullscreen: useMemoizedFn(toggleFullscreen),\n    isEnabled: screenfull.isEnabled\n  }];\n};\nexport default useFullscreen;", "import { __assign } from \"tslib\";\nexport var fieldAdapter = function (field) {\n  return {\n    getFieldInstance: function (name) {\n      return field.getNames().includes(name);\n    },\n    setFieldsValue: field.setValues,\n    getFieldsValue: field.getValues,\n    resetFields: field.resetToDefault,\n    validateFields: function (fields, callback) {\n      field.validate(fields, callback);\n    }\n  };\n};\nexport var resultAdapter = function (result) {\n  var tableProps = {\n    dataSource: result.tableProps.dataSource,\n    loading: result.tableProps.loading,\n    onSort: function (dataIndex, order) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.filters, {\n        field: dataIndex,\n        order: order\n      });\n    },\n    onFilter: function (filterParams) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, filterParams, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.sorter);\n    }\n  };\n  var paginationProps = {\n    onChange: result.pagination.changeCurrent,\n    onPageSizeChange: result.pagination.changePageSize,\n    current: result.pagination.current,\n    pageSize: result.pagination.pageSize,\n    total: result.pagination.total\n  };\n  return __assign(__assign({}, result), {\n    tableProps: tableProps,\n    paginationProps: paginationProps\n  });\n};", "import { __assign } from \"tslib\";\nimport useAntdTable from '../useAntdTable';\nimport { fieldAdapter, resultAdapter } from './fusionAdapter';\nvar useFusionTable = function (service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var ret = useAntdTable(service, __assign(__assign({}, options), {\n    form: options.field ? fieldAdapter(options.field) : undefined\n  }));\n  return resultAdapter(ret);\n};\nexport default useFusionTable;", "import { __read } from \"tslib\";\nimport { useState, useCallback } from 'react';\nimport useLatest from '../useLatest';\nfunction useGetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useLatest(state);\n  var getState = useCallback(function () {\n    return stateRef.current;\n  }, []);\n  return [state, setState, getState];\n}\nexport default useGetState;", "import { __read, __spreadArray } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar dumpIndex = function (step, arr) {\n  var index = step > 0 ? step - 1 // move forward\n  : arr.length + step; // move backward\n  if (index >= arr.length - 1) {\n    index = arr.length - 1;\n  }\n  if (index < 0) {\n    index = 0;\n  }\n  return index;\n};\nvar split = function (step, targetArr) {\n  var index = dumpIndex(step, targetArr);\n  return {\n    _current: targetArr[index],\n    _before: targetArr.slice(0, index),\n    _after: targetArr.slice(index + 1)\n  };\n};\nexport default function useHistoryTravel(initialValue, maxLength) {\n  if (maxLength === void 0) {\n    maxLength = 0;\n  }\n  var _a = __read(useState({\n      present: initialValue,\n      past: [],\n      future: []\n    }), 2),\n    history = _a[0],\n    setHistory = _a[1];\n  var present = history.present,\n    past = history.past,\n    future = history.future;\n  var initialValueRef = useRef(initialValue);\n  var reset = function () {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    var _initial = params.length > 0 ? params[0] : initialValueRef.current;\n    initialValueRef.current = _initial;\n    setHistory({\n      present: _initial,\n      future: [],\n      past: []\n    });\n  };\n  var updateValue = function (val) {\n    var _past = __spreadArray(__spreadArray([], __read(past), false), [present], false);\n    var maxLengthNum = isNumber(maxLength) ? maxLength : Number(maxLength);\n    // maximum number of records exceeded\n    if (maxLengthNum > 0 && _past.length > maxLengthNum) {\n      //delete first\n      _past.splice(0, 1);\n    }\n    setHistory({\n      present: val,\n      future: [],\n      past: _past\n    });\n  };\n  var _forward = function (step) {\n    if (step === void 0) {\n      step = 1;\n    }\n    if (future.length === 0) {\n      return;\n    }\n    var _a = split(step, future),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: __spreadArray(__spreadArray(__spreadArray([], __read(past), false), [present], false), __read(_before), false),\n      present: _current,\n      future: _after\n    });\n  };\n  var _backward = function (step) {\n    if (step === void 0) {\n      step = -1;\n    }\n    if (past.length === 0) {\n      return;\n    }\n    var _a = split(step, past),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: _before,\n      present: _current,\n      future: __spreadArray(__spreadArray(__spreadArray([], __read(_after), false), [present], false), __read(future), false)\n    });\n  };\n  var go = function (step) {\n    var stepNum = isNumber(step) ? step : Number(step);\n    if (stepNum === 0) {\n      return;\n    }\n    if (stepNum > 0) {\n      return _forward(stepNum);\n    }\n    _backward(stepNum);\n  };\n  return {\n    value: present,\n    backLength: past.length,\n    forwardLength: future.length,\n    setValue: useMemoizedFn(updateValue),\n    go: useMemoizedFn(go),\n    back: useMemoizedFn(function () {\n      go(-1);\n    }),\n    forward: useMemoizedFn(function () {\n      go(1);\n    }),\n    reset: useMemoizedFn(reset)\n  };\n}", "import { __read } from \"tslib\";\nimport useBoolean from '../useBoolean';\nimport useEventListener from '../useEventListener';\nexport default (function (target, options) {\n  var _a = options || {},\n    onEnter = _a.onEnter,\n    onLeave = _a.onLeave,\n    onChange = _a.onChange;\n  var _b = __read(useBoolean(false), 2),\n    state = _b[0],\n    _c = _b[1],\n    setTrue = _c.setTrue,\n    setFalse = _c.setFalse;\n  useEventListener('mouseenter', function () {\n    onEnter === null || onEnter === void 0 ? void 0 : onEnter();\n    setTrue();\n    onChange === null || onChange === void 0 ? void 0 : onChange(true);\n  }, {\n    target: target\n  });\n  useEventListener('mouseleave', function () {\n    onLeave === null || onLeave === void 0 ? void 0 : onLeave();\n    setFalse();\n    onChange === null || onChange === void 0 ? void 0 : onChange(false);\n  }, {\n    target: target\n  });\n  return state;\n});", "import { __assign, __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { useMemo, useRef, useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { getTargetElement } from '../utils/domTarget';\nimport { getClientHeight, getScrollHeight, getScrollTop } from '../utils/rect';\nvar useInfiniteScroll = function (service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var target = options.target,\n    isNoMore = options.isNoMore,\n    _a = options.threshold,\n    threshold = _a === void 0 ? 100 : _a,\n    _b = options.direction,\n    direction = _b === void 0 ? 'bottom' : _b,\n    _c = options.reloadDeps,\n    reloadDeps = _c === void 0 ? [] : _c,\n    manual = options.manual,\n    onBefore = options.onBefore,\n    onSuccess = options.onSuccess,\n    onError = options.onError,\n    onFinally = options.onFinally;\n  var _d = __read(useState(), 2),\n    finalData = _d[0],\n    setFinalData = _d[1];\n  var _e = __read(useState(false), 2),\n    loadingMore = _e[0],\n    setLoadingMore = _e[1];\n  var isScrollToTop = direction === 'top';\n  // lastScrollTop is used to determine whether the scroll direction is up or down\n  var lastScrollTop = useRef(undefined);\n  // scrollBottom is used to record the distance from the bottom of the scroll bar\n  var scrollBottom = useRef(0);\n  var noMore = useMemo(function () {\n    if (!isNoMore) {\n      return false;\n    }\n    return isNoMore(finalData);\n  }, [finalData]);\n  var _f = useRequest(function (lastData) {\n      return __awaiter(void 0, void 0, void 0, function () {\n        var currentData;\n        var _a, _b, _c;\n        return __generator(this, function (_d) {\n          switch (_d.label) {\n            case 0:\n              return [4 /*yield*/, service(lastData)];\n            case 1:\n              currentData = _d.sent();\n              if (!lastData) {\n                setFinalData(__assign(__assign({}, currentData), {\n                  list: __spreadArray([], __read((_a = currentData.list) !== null && _a !== void 0 ? _a : []), false)\n                }));\n              } else {\n                setFinalData(__assign(__assign({}, currentData), {\n                  list: isScrollToTop ? __spreadArray(__spreadArray([], __read(currentData.list), false), __read((_b = lastData.list) !== null && _b !== void 0 ? _b : []), false) : __spreadArray(__spreadArray([], __read((_c = lastData.list) !== null && _c !== void 0 ? _c : []), false), __read(currentData.list), false)\n                }));\n              }\n              return [2 /*return*/, currentData];\n          }\n        });\n      });\n    }, {\n      manual: manual,\n      onFinally: function (_, d, e) {\n        setLoadingMore(false);\n        onFinally === null || onFinally === void 0 ? void 0 : onFinally(d, e);\n      },\n      onBefore: function () {\n        return onBefore === null || onBefore === void 0 ? void 0 : onBefore();\n      },\n      onSuccess: function (d) {\n        setTimeout(function () {\n          if (isScrollToTop) {\n            var el = getTargetElement(target);\n            el = el === document ? document.documentElement : el;\n            if (el) {\n              var scrollHeight = getScrollHeight(el);\n              el.scrollTo(0, scrollHeight - scrollBottom.current);\n            }\n          } else {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            scrollMethod();\n          }\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(d);\n      },\n      onError: function (e) {\n        return onError === null || onError === void 0 ? void 0 : onError(e);\n      }\n    }),\n    loading = _f.loading,\n    error = _f.error,\n    run = _f.run,\n    runAsync = _f.runAsync,\n    cancel = _f.cancel;\n  var loadMore = useMemoizedFn(function () {\n    if (noMore) {\n      return;\n    }\n    setLoadingMore(true);\n    run(finalData);\n  });\n  var loadMoreAsync = useMemoizedFn(function () {\n    if (noMore) {\n      return Promise.reject();\n    }\n    setLoadingMore(true);\n    return runAsync(finalData);\n  });\n  var reload = function () {\n    setLoadingMore(false);\n    return run();\n  };\n  var reloadAsync = function () {\n    setLoadingMore(false);\n    return runAsync();\n  };\n  var scrollMethod = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var targetEl = el === document ? document.documentElement : el;\n    var scrollTop = getScrollTop(targetEl);\n    var scrollHeight = getScrollHeight(targetEl);\n    var clientHeight = getClientHeight(targetEl);\n    if (isScrollToTop) {\n      if (lastScrollTop.current !== undefined && lastScrollTop.current > scrollTop && scrollTop <= threshold) {\n        loadMore();\n      }\n      lastScrollTop.current = scrollTop;\n      scrollBottom.current = scrollHeight - scrollTop;\n    } else if (scrollHeight - scrollTop <= clientHeight + threshold) {\n      loadMore();\n    }\n  };\n  useEventListener('scroll', function () {\n    if (loading || loadingMore) {\n      return;\n    }\n    scrollMethod();\n  }, {\n    target: target\n  });\n  useUpdateEffect(function () {\n    run();\n  }, __spreadArray([], __read(reloadDeps), false));\n  return {\n    data: finalData,\n    loading: !loadingMore && loading,\n    error: error,\n    loadingMore: loadingMore,\n    noMore: noMore,\n    loadMore: loadMore,\n    loadMoreAsync: loadMoreAsync,\n    reload: useMemoizedFn(reload),\n    reloadAsync: useMemoizedFn(reloadAsync),\n    mutate: setFinalData,\n    cancel: cancel\n  };\n};\nexport default useInfiniteScroll;", "var getScrollTop = function (el) {\n  if (el === document || el === document.documentElement || el === document.body) {\n    return Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop);\n  }\n  return el.scrollTop;\n};\nvar getScrollHeight = function (el) {\n  return el.scrollHeight || Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);\n};\nvar getClientHeight = function (el) {\n  return el.clientHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight);\n};\nexport { getScrollTop, getScrollHeight, getClientHeight };", "import { useCallback, useEffect, useRef } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar useInterval = function (fn, delay, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var timerCallback = useMemoizedFn(fn);\n  var timerRef = useRef(null);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearInterval(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (options.immediate) {\n      timerCallback();\n    }\n    timerRef.current = setInterval(timerCallback, delay);\n    return clear;\n  }, [delay, options.immediate]);\n  return clear;\n};\nexport default useInterval;", "/**\n * Copyright 2016 Google Inc. All Rights Reserved.\n *\n * Licensed under the W3C SOFTWARE AND DOCUMENT NOTICE AND LICENSE.\n *\n *  https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n *\n */\n(function() {\n'use strict';\n\n// Exit early if we're not running in a browser.\nif (typeof window !== 'object') {\n  return;\n}\n\n// Exit early if all IntersectionObserver and IntersectionObserverEntry\n// features are natively supported.\nif ('IntersectionObserver' in window &&\n    'IntersectionObserverEntry' in window &&\n    'intersectionRatio' in window.IntersectionObserverEntry.prototype) {\n\n  // Minimal polyfill for Edge 15's lack of `isIntersecting`\n  // See: https://github.com/w3c/IntersectionObserver/issues/211\n  if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {\n    Object.defineProperty(window.IntersectionObserverEntry.prototype,\n      'isIntersecting', {\n      get: function () {\n        return this.intersectionRatio > 0;\n      }\n    });\n  }\n  return;\n}\n\n/**\n * Returns the embedding frame element, if any.\n * @param {!Document} doc\n * @return {!Element}\n */\nfunction getFrameElement(doc) {\n  try {\n    return doc.defaultView && doc.defaultView.frameElement || null;\n  } catch (e) {\n    // Ignore the error.\n    return null;\n  }\n}\n\n/**\n * A local reference to the root document.\n */\nvar document = (function(startDoc) {\n  var doc = startDoc;\n  var frame = getFrameElement(doc);\n  while (frame) {\n    doc = frame.ownerDocument;\n    frame = getFrameElement(doc);\n  }\n  return doc;\n})(window.document);\n\n/**\n * An IntersectionObserver registry. This registry exists to hold a strong\n * reference to IntersectionObserver instances currently observing a target\n * element. Without this registry, instances without another reference may be\n * garbage collected.\n */\nvar registry = [];\n\n/**\n * The signal updater for cross-origin intersection. When not null, it means\n * that the polyfill is configured to work in a cross-origin mode.\n * @type {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nvar crossOriginUpdater = null;\n\n/**\n * The current cross-origin intersection. Only used in the cross-origin mode.\n * @type {DOMRect|ClientRect}\n */\nvar crossOriginRect = null;\n\n\n/**\n * Creates the global IntersectionObserverEntry constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-entry\n * @param {Object} entry A dictionary of instance properties.\n * @constructor\n */\nfunction IntersectionObserverEntry(entry) {\n  this.time = entry.time;\n  this.target = entry.target;\n  this.rootBounds = ensureDOMRect(entry.rootBounds);\n  this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);\n  this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());\n  this.isIntersecting = !!entry.intersectionRect;\n\n  // Calculates the intersection ratio.\n  var targetRect = this.boundingClientRect;\n  var targetArea = targetRect.width * targetRect.height;\n  var intersectionRect = this.intersectionRect;\n  var intersectionArea = intersectionRect.width * intersectionRect.height;\n\n  // Sets intersection ratio.\n  if (targetArea) {\n    // Round the intersection ratio to avoid floating point math issues:\n    // https://github.com/w3c/IntersectionObserver/issues/324\n    this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));\n  } else {\n    // If area is zero and is intersecting, sets to 1, otherwise to 0\n    this.intersectionRatio = this.isIntersecting ? 1 : 0;\n  }\n}\n\n\n/**\n * Creates the global IntersectionObserver constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-interface\n * @param {Function} callback The function to be invoked after intersection\n *     changes have queued. The function is not invoked if the queue has\n *     been emptied by calling the `takeRecords` method.\n * @param {Object=} opt_options Optional configuration options.\n * @constructor\n */\nfunction IntersectionObserver(callback, opt_options) {\n\n  var options = opt_options || {};\n\n  if (typeof callback != 'function') {\n    throw new Error('callback must be a function');\n  }\n\n  if (\n    options.root &&\n    options.root.nodeType != 1 &&\n    options.root.nodeType != 9\n  ) {\n    throw new Error('root must be a Document or Element');\n  }\n\n  // Binds and throttles `this._checkForIntersections`.\n  this._checkForIntersections = throttle(\n      this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT);\n\n  // Private properties.\n  this._callback = callback;\n  this._observationTargets = [];\n  this._queuedEntries = [];\n  this._rootMarginValues = this._parseRootMargin(options.rootMargin);\n\n  // Public properties.\n  this.thresholds = this._initThresholds(options.threshold);\n  this.root = options.root || null;\n  this.rootMargin = this._rootMarginValues.map(function(margin) {\n    return margin.value + margin.unit;\n  }).join(' ');\n\n  /** @private @const {!Array<!Document>} */\n  this._monitoringDocuments = [];\n  /** @private @const {!Array<function()>} */\n  this._monitoringUnsubscribes = [];\n}\n\n\n/**\n * The minimum interval within which the document will be checked for\n * intersection changes.\n */\nIntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;\n\n\n/**\n * The frequency in which the polyfill polls for intersection changes.\n * this can be updated on a per instance basis and must be set prior to\n * calling `observe` on the first target.\n */\nIntersectionObserver.prototype.POLL_INTERVAL = null;\n\n/**\n * Use a mutation observer on the root element\n * to detect intersection changes.\n */\nIntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;\n\n\n/**\n * Sets up the polyfill in the cross-origin mode. The result is the\n * updater function that accepts two arguments: `boundingClientRect` and\n * `intersectionRect` - just as these fields would be available to the\n * parent via `IntersectionObserverEntry`. This function should be called\n * each time the iframe receives intersection information from the parent\n * window, e.g. via messaging.\n * @return {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nIntersectionObserver._setupCrossOriginUpdater = function() {\n  if (!crossOriginUpdater) {\n    /**\n     * @param {DOMRect|ClientRect} boundingClientRect\n     * @param {DOMRect|ClientRect} intersectionRect\n     */\n    crossOriginUpdater = function(boundingClientRect, intersectionRect) {\n      if (!boundingClientRect || !intersectionRect) {\n        crossOriginRect = getEmptyRect();\n      } else {\n        crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);\n      }\n      registry.forEach(function(observer) {\n        observer._checkForIntersections();\n      });\n    };\n  }\n  return crossOriginUpdater;\n};\n\n\n/**\n * Resets the cross-origin mode.\n */\nIntersectionObserver._resetCrossOriginUpdater = function() {\n  crossOriginUpdater = null;\n  crossOriginRect = null;\n};\n\n\n/**\n * Starts observing a target element for intersection changes based on\n * the thresholds values.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.observe = function(target) {\n  var isTargetAlreadyObserved = this._observationTargets.some(function(item) {\n    return item.element == target;\n  });\n\n  if (isTargetAlreadyObserved) {\n    return;\n  }\n\n  if (!(target && target.nodeType == 1)) {\n    throw new Error('target must be an Element');\n  }\n\n  this._registerInstance();\n  this._observationTargets.push({element: target, entry: null});\n  this._monitorIntersections(target.ownerDocument);\n  this._checkForIntersections();\n};\n\n\n/**\n * Stops observing a target element for intersection changes.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.unobserve = function(target) {\n  this._observationTargets =\n      this._observationTargets.filter(function(item) {\n        return item.element != target;\n      });\n  this._unmonitorIntersections(target.ownerDocument);\n  if (this._observationTargets.length == 0) {\n    this._unregisterInstance();\n  }\n};\n\n\n/**\n * Stops observing all target elements for intersection changes.\n */\nIntersectionObserver.prototype.disconnect = function() {\n  this._observationTargets = [];\n  this._unmonitorAllIntersections();\n  this._unregisterInstance();\n};\n\n\n/**\n * Returns any queue entries that have not yet been reported to the\n * callback and clears the queue. This can be used in conjunction with the\n * callback to obtain the absolute most up-to-date intersection information.\n * @return {Array} The currently queued entries.\n */\nIntersectionObserver.prototype.takeRecords = function() {\n  var records = this._queuedEntries.slice();\n  this._queuedEntries = [];\n  return records;\n};\n\n\n/**\n * Accepts the threshold value from the user configuration object and\n * returns a sorted array of unique threshold values. If a value is not\n * between 0 and 1 and error is thrown.\n * @private\n * @param {Array|number=} opt_threshold An optional threshold value or\n *     a list of threshold values, defaulting to [0].\n * @return {Array} A sorted list of unique and valid threshold values.\n */\nIntersectionObserver.prototype._initThresholds = function(opt_threshold) {\n  var threshold = opt_threshold || [0];\n  if (!Array.isArray(threshold)) threshold = [threshold];\n\n  return threshold.sort().filter(function(t, i, a) {\n    if (typeof t != 'number' || isNaN(t) || t < 0 || t > 1) {\n      throw new Error('threshold must be a number between 0 and 1 inclusively');\n    }\n    return t !== a[i - 1];\n  });\n};\n\n\n/**\n * Accepts the rootMargin value from the user configuration object\n * and returns an array of the four margin values as an object containing\n * the value and unit properties. If any of the values are not properly\n * formatted or use a unit other than px or %, and error is thrown.\n * @private\n * @param {string=} opt_rootMargin An optional rootMargin value,\n *     defaulting to '0px'.\n * @return {Array<Object>} An array of margin objects with the keys\n *     value and unit.\n */\nIntersectionObserver.prototype._parseRootMargin = function(opt_rootMargin) {\n  var marginString = opt_rootMargin || '0px';\n  var margins = marginString.split(/\\s+/).map(function(margin) {\n    var parts = /^(-?\\d*\\.?\\d+)(px|%)$/.exec(margin);\n    if (!parts) {\n      throw new Error('rootMargin must be specified in pixels or percent');\n    }\n    return {value: parseFloat(parts[1]), unit: parts[2]};\n  });\n\n  // Handles shorthand.\n  margins[1] = margins[1] || margins[0];\n  margins[2] = margins[2] || margins[0];\n  margins[3] = margins[3] || margins[1];\n\n  return margins;\n};\n\n\n/**\n * Starts polling for intersection changes if the polling is not already\n * happening, and if the page's visibility state is visible.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._monitorIntersections = function(doc) {\n  var win = doc.defaultView;\n  if (!win) {\n    // Already destroyed.\n    return;\n  }\n  if (this._monitoringDocuments.indexOf(doc) != -1) {\n    // Already monitoring.\n    return;\n  }\n\n  // Private state for monitoring.\n  var callback = this._checkForIntersections;\n  var monitoringInterval = null;\n  var domObserver = null;\n\n  // If a poll interval is set, use polling instead of listening to\n  // resize and scroll events or DOM mutations.\n  if (this.POLL_INTERVAL) {\n    monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);\n  } else {\n    addEvent(win, 'resize', callback, true);\n    addEvent(doc, 'scroll', callback, true);\n    if (this.USE_MUTATION_OBSERVER && 'MutationObserver' in win) {\n      domObserver = new win.MutationObserver(callback);\n      domObserver.observe(doc, {\n        attributes: true,\n        childList: true,\n        characterData: true,\n        subtree: true\n      });\n    }\n  }\n\n  this._monitoringDocuments.push(doc);\n  this._monitoringUnsubscribes.push(function() {\n    // Get the window object again. When a friendly iframe is destroyed, it\n    // will be null.\n    var win = doc.defaultView;\n\n    if (win) {\n      if (monitoringInterval) {\n        win.clearInterval(monitoringInterval);\n      }\n      removeEvent(win, 'resize', callback, true);\n    }\n\n    removeEvent(doc, 'scroll', callback, true);\n    if (domObserver) {\n      domObserver.disconnect();\n    }\n  });\n\n  // Also monitor the parent.\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._monitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorIntersections = function(doc) {\n  var index = this._monitoringDocuments.indexOf(doc);\n  if (index == -1) {\n    return;\n  }\n\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n\n  // Check if any dependent targets are still remaining.\n  var hasDependentTargets =\n      this._observationTargets.some(function(item) {\n        var itemDoc = item.element.ownerDocument;\n        // Target is in this context.\n        if (itemDoc == doc) {\n          return true;\n        }\n        // Target is nested in this context.\n        while (itemDoc && itemDoc != rootDoc) {\n          var frame = getFrameElement(itemDoc);\n          itemDoc = frame && frame.ownerDocument;\n          if (itemDoc == doc) {\n            return true;\n          }\n        }\n        return false;\n      });\n  if (hasDependentTargets) {\n    return;\n  }\n\n  // Unsubscribe.\n  var unsubscribe = this._monitoringUnsubscribes[index];\n  this._monitoringDocuments.splice(index, 1);\n  this._monitoringUnsubscribes.splice(index, 1);\n  unsubscribe();\n\n  // Also unmonitor the parent.\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._unmonitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorAllIntersections = function() {\n  var unsubscribes = this._monitoringUnsubscribes.slice(0);\n  this._monitoringDocuments.length = 0;\n  this._monitoringUnsubscribes.length = 0;\n  for (var i = 0; i < unsubscribes.length; i++) {\n    unsubscribes[i]();\n  }\n};\n\n\n/**\n * Scans each observation target for intersection changes and adds them\n * to the internal entries queue. If new entries are found, it\n * schedules the callback to be invoked.\n * @private\n */\nIntersectionObserver.prototype._checkForIntersections = function() {\n  if (!this.root && crossOriginUpdater && !crossOriginRect) {\n    // Cross origin monitoring, but no initial data available yet.\n    return;\n  }\n\n  var rootIsInDom = this._rootIsInDom();\n  var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();\n\n  this._observationTargets.forEach(function(item) {\n    var target = item.element;\n    var targetRect = getBoundingClientRect(target);\n    var rootContainsTarget = this._rootContainsTarget(target);\n    var oldEntry = item.entry;\n    var intersectionRect = rootIsInDom && rootContainsTarget &&\n        this._computeTargetAndRootIntersection(target, targetRect, rootRect);\n\n    var rootBounds = null;\n    if (!this._rootContainsTarget(target)) {\n      rootBounds = getEmptyRect();\n    } else if (!crossOriginUpdater || this.root) {\n      rootBounds = rootRect;\n    }\n\n    var newEntry = item.entry = new IntersectionObserverEntry({\n      time: now(),\n      target: target,\n      boundingClientRect: targetRect,\n      rootBounds: rootBounds,\n      intersectionRect: intersectionRect\n    });\n\n    if (!oldEntry) {\n      this._queuedEntries.push(newEntry);\n    } else if (rootIsInDom && rootContainsTarget) {\n      // If the new entry intersection ratio has crossed any of the\n      // thresholds, add a new entry.\n      if (this._hasCrossedThreshold(oldEntry, newEntry)) {\n        this._queuedEntries.push(newEntry);\n      }\n    } else {\n      // If the root is not in the DOM or target is not contained within\n      // root but the previous entry for this target had an intersection,\n      // add a new record indicating removal.\n      if (oldEntry && oldEntry.isIntersecting) {\n        this._queuedEntries.push(newEntry);\n      }\n    }\n  }, this);\n\n  if (this._queuedEntries.length) {\n    this._callback(this.takeRecords(), this);\n  }\n};\n\n\n/**\n * Accepts a target and root rect computes the intersection between then\n * following the algorithm in the spec.\n * TODO(philipwalton): at this time clip-path is not considered.\n * https://w3c.github.io/IntersectionObserver/#calculate-intersection-rect-algo\n * @param {Element} target The target DOM element\n * @param {Object} targetRect The bounding rect of the target.\n * @param {Object} rootRect The bounding rect of the root after being\n *     expanded by the rootMargin value.\n * @return {?Object} The final intersection rect object or undefined if no\n *     intersection is found.\n * @private\n */\nIntersectionObserver.prototype._computeTargetAndRootIntersection =\n    function(target, targetRect, rootRect) {\n  // If the element isn't displayed, an intersection can't happen.\n  if (window.getComputedStyle(target).display == 'none') return;\n\n  var intersectionRect = targetRect;\n  var parent = getParentNode(target);\n  var atRoot = false;\n\n  while (!atRoot && parent) {\n    var parentRect = null;\n    var parentComputedStyle = parent.nodeType == 1 ?\n        window.getComputedStyle(parent) : {};\n\n    // If the parent isn't displayed, an intersection can't happen.\n    if (parentComputedStyle.display == 'none') return null;\n\n    if (parent == this.root || parent.nodeType == /* DOCUMENT */ 9) {\n      atRoot = true;\n      if (parent == this.root || parent == document) {\n        if (crossOriginUpdater && !this.root) {\n          if (!crossOriginRect ||\n              crossOriginRect.width == 0 && crossOriginRect.height == 0) {\n            // A 0-size cross-origin intersection means no-intersection.\n            parent = null;\n            parentRect = null;\n            intersectionRect = null;\n          } else {\n            parentRect = crossOriginRect;\n          }\n        } else {\n          parentRect = rootRect;\n        }\n      } else {\n        // Check if there's a frame that can be navigated to.\n        var frame = getParentNode(parent);\n        var frameRect = frame && getBoundingClientRect(frame);\n        var frameIntersect =\n            frame &&\n            this._computeTargetAndRootIntersection(frame, frameRect, rootRect);\n        if (frameRect && frameIntersect) {\n          parent = frame;\n          parentRect = convertFromParentRect(frameRect, frameIntersect);\n        } else {\n          parent = null;\n          intersectionRect = null;\n        }\n      }\n    } else {\n      // If the element has a non-visible overflow, and it's not the <body>\n      // or <html> element, update the intersection rect.\n      // Note: <body> and <html> cannot be clipped to a rect that's not also\n      // the document rect, so no need to compute a new intersection.\n      var doc = parent.ownerDocument;\n      if (parent != doc.body &&\n          parent != doc.documentElement &&\n          parentComputedStyle.overflow != 'visible') {\n        parentRect = getBoundingClientRect(parent);\n      }\n    }\n\n    // If either of the above conditionals set a new parentRect,\n    // calculate new intersection data.\n    if (parentRect) {\n      intersectionRect = computeRectIntersection(parentRect, intersectionRect);\n    }\n    if (!intersectionRect) break;\n    parent = parent && getParentNode(parent);\n  }\n  return intersectionRect;\n};\n\n\n/**\n * Returns the root rect after being expanded by the rootMargin value.\n * @return {ClientRect} The expanded root rect.\n * @private\n */\nIntersectionObserver.prototype._getRootRect = function() {\n  var rootRect;\n  if (this.root && !isDoc(this.root)) {\n    rootRect = getBoundingClientRect(this.root);\n  } else {\n    // Use <html>/<body> instead of window since scroll bars affect size.\n    var doc = isDoc(this.root) ? this.root : document;\n    var html = doc.documentElement;\n    var body = doc.body;\n    rootRect = {\n      top: 0,\n      left: 0,\n      right: html.clientWidth || body.clientWidth,\n      width: html.clientWidth || body.clientWidth,\n      bottom: html.clientHeight || body.clientHeight,\n      height: html.clientHeight || body.clientHeight\n    };\n  }\n  return this._expandRectByRootMargin(rootRect);\n};\n\n\n/**\n * Accepts a rect and expands it by the rootMargin value.\n * @param {DOMRect|ClientRect} rect The rect object to expand.\n * @return {ClientRect} The expanded rect.\n * @private\n */\nIntersectionObserver.prototype._expandRectByRootMargin = function(rect) {\n  var margins = this._rootMarginValues.map(function(margin, i) {\n    return margin.unit == 'px' ? margin.value :\n        margin.value * (i % 2 ? rect.width : rect.height) / 100;\n  });\n  var newRect = {\n    top: rect.top - margins[0],\n    right: rect.right + margins[1],\n    bottom: rect.bottom + margins[2],\n    left: rect.left - margins[3]\n  };\n  newRect.width = newRect.right - newRect.left;\n  newRect.height = newRect.bottom - newRect.top;\n\n  return newRect;\n};\n\n\n/**\n * Accepts an old and new entry and returns true if at least one of the\n * threshold values has been crossed.\n * @param {?IntersectionObserverEntry} oldEntry The previous entry for a\n *    particular target element or null if no previous entry exists.\n * @param {IntersectionObserverEntry} newEntry The current entry for a\n *    particular target element.\n * @return {boolean} Returns true if a any threshold has been crossed.\n * @private\n */\nIntersectionObserver.prototype._hasCrossedThreshold =\n    function(oldEntry, newEntry) {\n\n  // To make comparing easier, an entry that has a ratio of 0\n  // but does not actually intersect is given a value of -1\n  var oldRatio = oldEntry && oldEntry.isIntersecting ?\n      oldEntry.intersectionRatio || 0 : -1;\n  var newRatio = newEntry.isIntersecting ?\n      newEntry.intersectionRatio || 0 : -1;\n\n  // Ignore unchanged ratios\n  if (oldRatio === newRatio) return;\n\n  for (var i = 0; i < this.thresholds.length; i++) {\n    var threshold = this.thresholds[i];\n\n    // Return true if an entry matches a threshold or if the new ratio\n    // and the old ratio are on the opposite sides of a threshold.\n    if (threshold == oldRatio || threshold == newRatio ||\n        threshold < oldRatio !== threshold < newRatio) {\n      return true;\n    }\n  }\n};\n\n\n/**\n * Returns whether or not the root element is an element and is in the DOM.\n * @return {boolean} True if the root element is an element and is in the DOM.\n * @private\n */\nIntersectionObserver.prototype._rootIsInDom = function() {\n  return !this.root || containsDeep(document, this.root);\n};\n\n\n/**\n * Returns whether or not the target element is a child of root.\n * @param {Element} target The target element to check.\n * @return {boolean} True if the target element is a child of root.\n * @private\n */\nIntersectionObserver.prototype._rootContainsTarget = function(target) {\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  return (\n    containsDeep(rootDoc, target) &&\n    (!this.root || rootDoc == target.ownerDocument)\n  );\n};\n\n\n/**\n * Adds the instance to the global IntersectionObserver registry if it isn't\n * already present.\n * @private\n */\nIntersectionObserver.prototype._registerInstance = function() {\n  if (registry.indexOf(this) < 0) {\n    registry.push(this);\n  }\n};\n\n\n/**\n * Removes the instance from the global IntersectionObserver registry.\n * @private\n */\nIntersectionObserver.prototype._unregisterInstance = function() {\n  var index = registry.indexOf(this);\n  if (index != -1) registry.splice(index, 1);\n};\n\n\n/**\n * Returns the result of the performance.now() method or null in browsers\n * that don't support the API.\n * @return {number} The elapsed time since the page was requested.\n */\nfunction now() {\n  return window.performance && performance.now && performance.now();\n}\n\n\n/**\n * Throttles a function and delays its execution, so it's only called at most\n * once within a given time period.\n * @param {Function} fn The function to throttle.\n * @param {number} timeout The amount of time that must pass before the\n *     function can be called again.\n * @return {Function} The throttled function.\n */\nfunction throttle(fn, timeout) {\n  var timer = null;\n  return function () {\n    if (!timer) {\n      timer = setTimeout(function() {\n        fn();\n        timer = null;\n      }, timeout);\n    }\n  };\n}\n\n\n/**\n * Adds an event handler to a DOM node ensuring cross-browser compatibility.\n * @param {Node} node The DOM node to add the event handler to.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to add.\n * @param {boolean} opt_useCapture Optionally adds the even to the capture\n *     phase. Note: this only works in modern browsers.\n */\nfunction addEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.addEventListener == 'function') {\n    node.addEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.attachEvent == 'function') {\n    node.attachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Removes a previously added event handler from a DOM node.\n * @param {Node} node The DOM node to remove the event handler from.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to remove.\n * @param {boolean} opt_useCapture If the event handler was added with this\n *     flag set to true, it should be set to true here in order to remove it.\n */\nfunction removeEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.removeEventListener == 'function') {\n    node.removeEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.detachEvent == 'function') {\n    node.detachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Returns the intersection between two rect objects.\n * @param {Object} rect1 The first rect.\n * @param {Object} rect2 The second rect.\n * @return {?Object|?ClientRect} The intersection rect or undefined if no\n *     intersection is found.\n */\nfunction computeRectIntersection(rect1, rect2) {\n  var top = Math.max(rect1.top, rect2.top);\n  var bottom = Math.min(rect1.bottom, rect2.bottom);\n  var left = Math.max(rect1.left, rect2.left);\n  var right = Math.min(rect1.right, rect2.right);\n  var width = right - left;\n  var height = bottom - top;\n\n  return (width >= 0 && height >= 0) && {\n    top: top,\n    bottom: bottom,\n    left: left,\n    right: right,\n    width: width,\n    height: height\n  } || null;\n}\n\n\n/**\n * Shims the native getBoundingClientRect for compatibility with older IE.\n * @param {Element} el The element whose bounding rect to get.\n * @return {DOMRect|ClientRect} The (possibly shimmed) rect of the element.\n */\nfunction getBoundingClientRect(el) {\n  var rect;\n\n  try {\n    rect = el.getBoundingClientRect();\n  } catch (err) {\n    // Ignore Windows 7 IE11 \"Unspecified error\"\n    // https://github.com/w3c/IntersectionObserver/pull/205\n  }\n\n  if (!rect) return getEmptyRect();\n\n  // Older IE\n  if (!(rect.width && rect.height)) {\n    rect = {\n      top: rect.top,\n      right: rect.right,\n      bottom: rect.bottom,\n      left: rect.left,\n      width: rect.right - rect.left,\n      height: rect.bottom - rect.top\n    };\n  }\n  return rect;\n}\n\n\n/**\n * Returns an empty rect object. An empty rect is returned when an element\n * is not in the DOM.\n * @return {ClientRect} The empty rect.\n */\nfunction getEmptyRect() {\n  return {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n    width: 0,\n    height: 0\n  };\n}\n\n\n/**\n * Ensure that the result has all of the necessary fields of the DOMRect.\n * Specifically this ensures that `x` and `y` fields are set.\n *\n * @param {?DOMRect|?ClientRect} rect\n * @return {?DOMRect}\n */\nfunction ensureDOMRect(rect) {\n  // A `DOMRect` object has `x` and `y` fields.\n  if (!rect || 'x' in rect) {\n    return rect;\n  }\n  // A IE's `ClientRect` type does not have `x` and `y`. The same is the case\n  // for internally calculated Rect objects. For the purposes of\n  // `IntersectionObserver`, it's sufficient to simply mirror `left` and `top`\n  // for these fields.\n  return {\n    top: rect.top,\n    y: rect.top,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    right: rect.right,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\n\n/**\n * Inverts the intersection and bounding rect from the parent (frame) BCR to\n * the local BCR space.\n * @param {DOMRect|ClientRect} parentBoundingRect The parent's bound client rect.\n * @param {DOMRect|ClientRect} parentIntersectionRect The parent's own intersection rect.\n * @return {ClientRect} The local root bounding rect for the parent's children.\n */\nfunction convertFromParentRect(parentBoundingRect, parentIntersectionRect) {\n  var top = parentIntersectionRect.top - parentBoundingRect.top;\n  var left = parentIntersectionRect.left - parentBoundingRect.left;\n  return {\n    top: top,\n    left: left,\n    height: parentIntersectionRect.height,\n    width: parentIntersectionRect.width,\n    bottom: top + parentIntersectionRect.height,\n    right: left + parentIntersectionRect.width\n  };\n}\n\n\n/**\n * Checks to see if a parent element contains a child element (including inside\n * shadow DOM).\n * @param {Node} parent The parent element.\n * @param {Node} child The child element.\n * @return {boolean} True if the parent node contains the child node.\n */\nfunction containsDeep(parent, child) {\n  var node = child;\n  while (node) {\n    if (node == parent) return true;\n\n    node = getParentNode(node);\n  }\n  return false;\n}\n\n\n/**\n * Gets the parent node of an element or its host element if the parent node\n * is a shadow root.\n * @param {Node} node The node whose parent to get.\n * @return {Node|null} The parent node or null if no parent exists.\n */\nfunction getParentNode(node) {\n  var parent = node.parentNode;\n\n  if (node.nodeType == /* DOCUMENT */ 9 && node != document) {\n    // If this node is a document node, look for the embedding frame.\n    return getFrameElement(node);\n  }\n\n  // If the parent has element that is assigned through shadow root slot\n  if (parent && parent.assignedSlot) {\n    parent = parent.assignedSlot.parentNode\n  }\n\n  if (parent && parent.nodeType == 11 && parent.host) {\n    // If the parent is a shadow root, return the host element.\n    return parent.host;\n  }\n\n  return parent;\n}\n\n/**\n * Returns true if `node` is a Document.\n * @param {!Node} node\n * @returns {boolean}\n */\nfunction isDoc(node) {\n  return node && node.nodeType === 9;\n}\n\n\n// Exposes the constructors globally.\nwindow.IntersectionObserver = IntersectionObserver;\nwindow.IntersectionObserverEntry = IntersectionObserverEntry;\n\n}());\n", "import { __assign, __read, __rest, __values } from \"tslib\";\nimport 'intersection-observer';\nimport { useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useInViewport(target, options) {\n  var _a = options || {},\n    callback = _a.callback,\n    option = __rest(_a, [\"callback\"]);\n  var _b = __read(useState(), 2),\n    state = _b[0],\n    setState = _b[1];\n  var _c = __read(useState(), 2),\n    ratio = _c[0],\n    setRatio = _c[1];\n  useEffectWithTarget(function () {\n    var targets = Array.isArray(target) ? target : [target];\n    var els = targets.map(function (element) {\n      return getTargetElement(element);\n    }).filter(Boolean);\n    if (!els.length) {\n      return;\n    }\n    var observer = new IntersectionObserver(function (entries) {\n      var e_1, _a;\n      try {\n        for (var entries_1 = __values(entries), entries_1_1 = entries_1.next(); !entries_1_1.done; entries_1_1 = entries_1.next()) {\n          var entry = entries_1_1.value;\n          setRatio(entry.intersectionRatio);\n          setState(entry.isIntersecting);\n          callback === null || callback === void 0 ? void 0 : callback(entry);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (entries_1_1 && !entries_1_1.done && (_a = entries_1.return)) _a.call(entries_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, __assign(__assign({}, option), {\n      root: getTargetElement(options === null || options === void 0 ? void 0 : options.root)\n    }));\n    els.forEach(function (el) {\n      return observer.observe(el);\n    });\n    return function () {\n      observer.disconnect();\n    };\n  }, [options === null || options === void 0 ? void 0 : options.rootMargin, options === null || options === void 0 ? void 0 : options.threshold, callback], target);\n  return [state, ratio];\n}\nexport default useInViewport;", "import { useEffect, useLayoutEffect } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;", "import { useRef } from 'react';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport { depsEqual } from './depsEqual';\nvar useDeepCompareEffectWithTarget = function (effect, deps, target) {\n  var ref = useRef(undefined);\n  var signalRef = useRef(0);\n  if (!depsEqual(deps, ref.current)) {\n    signalRef.current += 1;\n  }\n  ref.current = deps;\n  useEffectWithTarget(effect, [signalRef.current], target);\n};\nexport default useDeepCompareEffectWithTarget;", "var isAppleDevice = /(mac|iphone|ipod|ipad)/i.test(typeof navigator !== 'undefined' ? navigator === null || navigator === void 0 ? void 0 : navigator.platform : '');\nexport default isAppleDevice;", "import { __values } from \"tslib\";\nimport useLatest from '../useLatest';\nimport { isFunction, isNumber, isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport isAppleDevice from '../utils/isAppleDevice';\n// 键盘事件 keyCode 别名\nvar aliasKeyCodeMap = {\n  '0': 48,\n  '1': 49,\n  '2': 50,\n  '3': 51,\n  '4': 52,\n  '5': 53,\n  '6': 54,\n  '7': 55,\n  '8': 56,\n  '9': 57,\n  backspace: 8,\n  tab: 9,\n  enter: 13,\n  shift: 16,\n  ctrl: 17,\n  alt: 18,\n  pausebreak: 19,\n  capslock: 20,\n  esc: 27,\n  space: 32,\n  pageup: 33,\n  pagedown: 34,\n  end: 35,\n  home: 36,\n  leftarrow: 37,\n  uparrow: 38,\n  rightarrow: 39,\n  downarrow: 40,\n  insert: 45,\n  delete: 46,\n  a: 65,\n  b: 66,\n  c: 67,\n  d: 68,\n  e: 69,\n  f: 70,\n  g: 71,\n  h: 72,\n  i: 73,\n  j: 74,\n  k: 75,\n  l: 76,\n  m: 77,\n  n: 78,\n  o: 79,\n  p: 80,\n  q: 81,\n  r: 82,\n  s: 83,\n  t: 84,\n  u: 85,\n  v: 86,\n  w: 87,\n  x: 88,\n  y: 89,\n  z: 90,\n  leftwindowkey: 91,\n  rightwindowkey: 92,\n  meta: isAppleDevice ? [91, 93] : [91, 92],\n  selectkey: 93,\n  numpad0: 96,\n  numpad1: 97,\n  numpad2: 98,\n  numpad3: 99,\n  numpad4: 100,\n  numpad5: 101,\n  numpad6: 102,\n  numpad7: 103,\n  numpad8: 104,\n  numpad9: 105,\n  multiply: 106,\n  add: 107,\n  subtract: 109,\n  decimalpoint: 110,\n  divide: 111,\n  f1: 112,\n  f2: 113,\n  f3: 114,\n  f4: 115,\n  f5: 116,\n  f6: 117,\n  f7: 118,\n  f8: 119,\n  f9: 120,\n  f10: 121,\n  f11: 122,\n  f12: 123,\n  numlock: 144,\n  scrolllock: 145,\n  semicolon: 186,\n  equalsign: 187,\n  comma: 188,\n  dash: 189,\n  period: 190,\n  forwardslash: 191,\n  graveaccent: 192,\n  openbracket: 219,\n  backslash: 220,\n  closebracket: 221,\n  singlequote: 222\n};\n// 修饰键\nvar modifierKey = {\n  ctrl: function (event) {\n    return event.ctrlKey;\n  },\n  shift: function (event) {\n    return event.shiftKey;\n  },\n  alt: function (event) {\n    return event.altKey;\n  },\n  meta: function (event) {\n    if (event.type === 'keyup') {\n      return aliasKeyCodeMap.meta.includes(event.keyCode);\n    }\n    return event.metaKey;\n  }\n};\n// 判断合法的按键类型\nfunction isValidKeyType(value) {\n  return isString(value) || isNumber(value);\n}\n// 根据 event 计算激活键数量\nfunction countKeyByEvent(event) {\n  var countOfModifier = Object.keys(modifierKey).reduce(function (total, key) {\n    if (modifierKey[key](event)) {\n      return total + 1;\n    }\n    return total;\n  }, 0);\n  // 16 17 18 91 92 是修饰键的 keyCode，如果 keyCode 是修饰键，那么激活数量就是修饰键的数量，如果不是，那么就需要 +1\n  return [16, 17, 18, 91, 92].includes(event.keyCode) ? countOfModifier : countOfModifier + 1;\n}\n/**\n * 判断按键是否激活\n * @param [event: KeyboardEvent]键盘事件\n * @param [keyFilter: any] 当前键\n * @returns string | number | boolean\n */\nfunction genFilterKey(event, keyFilter, exactMatch) {\n  var e_1, _a;\n  // 浏览器自动补全 input 的时候，会触发 keyDown、keyUp 事件，但此时 event.key 等为空\n  if (!event.key) {\n    return false;\n  }\n  // 数字类型直接匹配事件的 keyCode\n  if (isNumber(keyFilter)) {\n    return event.keyCode === keyFilter ? keyFilter : false;\n  }\n  // 字符串依次判断是否有组合键\n  var genArr = keyFilter.split('.');\n  var genLen = 0;\n  try {\n    for (var genArr_1 = __values(genArr), genArr_1_1 = genArr_1.next(); !genArr_1_1.done; genArr_1_1 = genArr_1.next()) {\n      var key = genArr_1_1.value;\n      // 组合键\n      var genModifier = modifierKey[key];\n      // keyCode 别名\n      var aliasKeyCode = aliasKeyCodeMap[key.toLowerCase()];\n      if (genModifier && genModifier(event) || aliasKeyCode && aliasKeyCode === event.keyCode) {\n        genLen++;\n      }\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (genArr_1_1 && !genArr_1_1.done && (_a = genArr_1.return)) _a.call(genArr_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  /**\n   * 需要判断触发的键位和监听的键位完全一致，判断方法就是触发的键位里有且等于监听的键位\n   * genLen === genArr.length 能判断出来触发的键位里有监听的键位\n   * countKeyByEvent(event) === genArr.length 判断出来触发的键位数量里有且等于监听的键位数量\n   * 主要用来防止按组合键其子集也会触发的情况，例如监听 ctrl+a 会触发监听 ctrl 和 a 两个键的事件。\n   */\n  if (exactMatch) {\n    return genLen === genArr.length && countKeyByEvent(event) === genArr.length ? keyFilter : false;\n  }\n  return genLen === genArr.length ? keyFilter : false;\n}\n/**\n * 键盘输入预处理方法\n * @param [keyFilter: any] 当前键\n * @returns () => Boolean\n */\nfunction genKeyFormatter(keyFilter, exactMatch) {\n  if (isFunction(keyFilter)) {\n    return keyFilter;\n  }\n  if (isValidKeyType(keyFilter)) {\n    return function (event) {\n      return genFilterKey(event, keyFilter, exactMatch);\n    };\n  }\n  if (Array.isArray(keyFilter)) {\n    return function (event) {\n      return keyFilter.find(function (item) {\n        return genFilterKey(event, item, exactMatch);\n      });\n    };\n  }\n  return function () {\n    return Boolean(keyFilter);\n  };\n}\nvar defaultEvents = ['keydown'];\nfunction useKeyPress(keyFilter, eventHandler, option) {\n  var _a = option || {},\n    _b = _a.events,\n    events = _b === void 0 ? defaultEvents : _b,\n    target = _a.target,\n    _c = _a.exactMatch,\n    exactMatch = _c === void 0 ? false : _c,\n    _d = _a.useCapture,\n    useCapture = _d === void 0 ? false : _d;\n  var eventHandlerRef = useLatest(eventHandler);\n  var keyFilterRef = useLatest(keyFilter);\n  useDeepCompareEffectWithTarget(function () {\n    var e_2, _a;\n    var _b;\n    var el = getTargetElement(target, window);\n    if (!el) {\n      return;\n    }\n    var callbackHandler = function (event) {\n      var _a;\n      var genGuard = genKeyFormatter(keyFilterRef.current, exactMatch);\n      var keyGuard = genGuard(event);\n      var firedKey = isValidKeyType(keyGuard) ? keyGuard : event.key;\n      if (keyGuard) {\n        return (_a = eventHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(eventHandlerRef, event, firedKey);\n      }\n    };\n    try {\n      for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {\n        var eventName = events_1_1.value;\n        (_b = el === null || el === void 0 ? void 0 : el.addEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler, useCapture);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (events_1_1 && !events_1_1.done && (_a = events_1.return)) _a.call(events_1);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return function () {\n      var e_3, _a;\n      var _b;\n      try {\n        for (var events_2 = __values(events), events_2_1 = events_2.next(); !events_2_1.done; events_2_1 = events_2.next()) {\n          var eventName = events_2_1.value;\n          (_b = el === null || el === void 0 ? void 0 : el.removeEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler, useCapture);\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (events_2_1 && !events_2_1.done && (_a = events_2.return)) _a.call(events_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n    };\n  }, [events], target);\n}\nexport default useKeyPress;", "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { isFunction, isUndef } from '../utils';\nexport var SYNC_STORAGE_EVENT_NAME = 'AHOOKS_SYNC_STORAGE_EVENT_NAME';\nexport function createUseStorageState(getStorage) {\n  function useStorageState(key, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var storage;\n    var _a = options.listenStorageChange,\n      listenStorageChange = _a === void 0 ? false : _a,\n      _b = options.onError,\n      onError = _b === void 0 ? function (e) {\n        console.error(e);\n      } : _b;\n    // https://github.com/alibaba/hooks/issues/800\n    try {\n      storage = getStorage();\n    } catch (err) {\n      onError(err);\n    }\n    var serializer = function (value) {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    };\n    var deserializer = function (value) {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      return JSON.parse(value);\n    };\n    function getStoredValue() {\n      try {\n        var raw = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (raw) {\n          return deserializer(raw);\n        }\n      } catch (e) {\n        onError(e);\n      }\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }\n    var _c = __read(useState(getStoredValue), 2),\n      state = _c[0],\n      setState = _c[1];\n    useUpdateEffect(function () {\n      setState(getStoredValue());\n    }, [key]);\n    var updateState = function (value) {\n      var currentState = isFunction(value) ? value(state) : value;\n      if (!listenStorageChange) {\n        setState(currentState);\n      }\n      try {\n        var newValue = void 0;\n        var oldValue = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (isUndef(currentState)) {\n          newValue = null;\n          storage === null || storage === void 0 ? void 0 : storage.removeItem(key);\n        } else {\n          newValue = serializer(currentState);\n          storage === null || storage === void 0 ? void 0 : storage.setItem(key, newValue);\n        }\n        dispatchEvent(\n        // send custom event to communicate within same page\n        // importantly this should not be a StorageEvent since those cannot\n        // be constructed with a non-built-in storage area\n        new CustomEvent(SYNC_STORAGE_EVENT_NAME, {\n          detail: {\n            key: key,\n            newValue: newValue,\n            oldValue: oldValue,\n            storageArea: storage\n          }\n        }));\n      } catch (e) {\n        onError(e);\n      }\n    };\n    var syncState = function (event) {\n      if (event.key !== key || event.storageArea !== storage) {\n        return;\n      }\n      setState(getStoredValue());\n    };\n    var syncStateFromCustomEvent = function (event) {\n      syncState(event.detail);\n    };\n    // from another document\n    useEventListener('storage', syncState, {\n      enable: listenStorageChange\n    });\n    // from the same document but different hooks\n    useEventListener(SYNC_STORAGE_EVENT_NAME, syncStateFromCustomEvent, {\n      enable: listenStorageChange\n    });\n    return [state, useMemoizedFn(updateState)];\n  }\n  return useStorageState;\n}", "import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useLocalStorageState = createUseStorageState(function () {\n  return isBrowser ? localStorage : undefined;\n});\nexport default useLocalStorageState;", "import { __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { useRef, useCallback } from 'react';\nfunction useLockFn(fn) {\n  var _this = this;\n  var lockRef = useRef(false);\n  return useCallback(function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    return __awaiter(_this, void 0, void 0, function () {\n      var ret, e_1;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            if (lockRef.current) {\n              return [2 /*return*/];\n            }\n            lockRef.current = true;\n            _a.label = 1;\n          case 1:\n            _a.trys.push([1, 3, 4, 5]);\n            return [4 /*yield*/, fn.apply(void 0, __spreadArray([], __read(args), false))];\n          case 2:\n            ret = _a.sent();\n            return [2 /*return*/, ret];\n          case 3:\n            e_1 = _a.sent();\n            throw e_1;\n          case 4:\n            lockRef.current = false;\n            return [7 /*endfinally*/];\n          case 5:\n            return [2 /*return*/];\n        }\n      });\n    });\n  }, [fn]);\n}\nexport default useLockFn;", "import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useLongPress(onLongPress, target, _a) {\n  var _b = _a === void 0 ? {} : _a,\n    _c = _b.delay,\n    delay = _c === void 0 ? 300 : _c,\n    moveThreshold = _b.moveThreshold,\n    onClick = _b.onClick,\n    onLongPressEnd = _b.onLongPressEnd;\n  var onLongPressRef = useLatest(onLongPress);\n  var onClickRef = useLatest(onClick);\n  var onLongPressEndRef = useLatest(onLongPressEnd);\n  var timerRef = useRef(undefined);\n  var isTriggeredRef = useRef(false);\n  var pervPositionRef = useRef({\n    x: 0,\n    y: 0\n  });\n  var mousePressed = useRef(false);\n  var touchPressed = useRef(false);\n  var hasMoveThreshold = !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && moveThreshold.x > 0 || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && moveThreshold.y > 0);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var overThreshold = function (event) {\n      var _a = getClientPosition(event),\n        clientX = _a.clientX,\n        clientY = _a.clientY;\n      var offsetX = Math.abs(clientX - pervPositionRef.current.x);\n      var offsetY = Math.abs(clientY - pervPositionRef.current.y);\n      return !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && offsetX > moveThreshold.x || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && offsetY > moveThreshold.y);\n    };\n    function getClientPosition(event) {\n      if ('TouchEvent' in window && event instanceof TouchEvent) {\n        return {\n          clientX: event.touches[0].clientX,\n          clientY: event.touches[0].clientY\n        };\n      }\n      if (event instanceof MouseEvent) {\n        return {\n          clientX: event.clientX,\n          clientY: event.clientY\n        };\n      }\n      return {\n        clientX: 0,\n        clientY: 0\n      };\n    }\n    var createTimer = function (event) {\n      timerRef.current = setTimeout(function () {\n        onLongPressRef.current(event);\n        isTriggeredRef.current = true;\n      }, delay);\n    };\n    var onTouchStart = function (event) {\n      if (touchPressed.current) {\n        return;\n      }\n      touchPressed.current = true;\n      if (hasMoveThreshold) {\n        var _a = getClientPosition(event),\n          clientX = _a.clientX,\n          clientY = _a.clientY;\n        pervPositionRef.current.x = clientX;\n        pervPositionRef.current.y = clientY;\n      }\n      createTimer(event);\n    };\n    var onMouseDown = function (event) {\n      var _a;\n      if ((_a = event === null || event === void 0 ? void 0 : event.sourceCapabilities) === null || _a === void 0 ? void 0 : _a.firesTouchEvents) {\n        return;\n      }\n      mousePressed.current = true;\n      if (hasMoveThreshold) {\n        pervPositionRef.current.x = event.clientX;\n        pervPositionRef.current.y = event.clientY;\n      }\n      createTimer(event);\n    };\n    var onMove = function (event) {\n      if (timerRef.current && overThreshold(event)) {\n        clearTimeout(timerRef.current);\n        timerRef.current = undefined;\n      }\n    };\n    var onTouchEnd = function (event) {\n      var _a;\n      if (!touchPressed.current) {\n        return;\n      }\n      touchPressed.current = false;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        timerRef.current = undefined;\n      }\n      if (isTriggeredRef.current) {\n        (_a = onLongPressEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onLongPressEndRef, event);\n      } else if (onClickRef.current) {\n        onClickRef.current(event);\n      }\n      isTriggeredRef.current = false;\n    };\n    var onMouseUp = function (event) {\n      var _a, _b;\n      if ((_a = event === null || event === void 0 ? void 0 : event.sourceCapabilities) === null || _a === void 0 ? void 0 : _a.firesTouchEvents) {\n        return;\n      }\n      if (!mousePressed.current) {\n        return;\n      }\n      mousePressed.current = false;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        timerRef.current = undefined;\n      }\n      if (isTriggeredRef.current) {\n        (_b = onLongPressEndRef.current) === null || _b === void 0 ? void 0 : _b.call(onLongPressEndRef, event);\n      } else if (onClickRef.current) {\n        onClickRef.current(event);\n      }\n      isTriggeredRef.current = false;\n    };\n    var onMouseLeave = function (event) {\n      var _a;\n      if (!mousePressed.current) {\n        return;\n      }\n      mousePressed.current = false;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        timerRef.current = undefined;\n      }\n      if (isTriggeredRef.current) {\n        (_a = onLongPressEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onLongPressEndRef, event);\n        isTriggeredRef.current = false;\n      }\n    };\n    targetElement.addEventListener('mousedown', onMouseDown);\n    targetElement.addEventListener('mouseup', onMouseUp);\n    targetElement.addEventListener('mouseleave', onMouseLeave);\n    targetElement.addEventListener('touchstart', onTouchStart);\n    targetElement.addEventListener('touchend', onTouchEnd);\n    if (hasMoveThreshold) {\n      targetElement.addEventListener('mousemove', onMove);\n      targetElement.addEventListener('touchmove', onMove);\n    }\n    return function () {\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        isTriggeredRef.current = false;\n      }\n      targetElement.removeEventListener('mousedown', onMouseDown);\n      targetElement.removeEventListener('mouseup', onMouseUp);\n      targetElement.removeEventListener('mouseleave', onMouseLeave);\n      targetElement.removeEventListener('touchstart', onTouchStart);\n      targetElement.removeEventListener('touchend', onTouchEnd);\n      if (hasMoveThreshold) {\n        targetElement.removeEventListener('mousemove', onMove);\n        targetElement.removeEventListener('touchmove', onMove);\n      }\n    };\n  }, [], target);\n}\nexport default useLongPress;", "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useMap(initialValue) {\n  var getInitValue = function () {\n    return new Map(initialValue);\n  };\n  var _a = __read(useState(getInitValue), 2),\n    map = _a[0],\n    setMap = _a[1];\n  var set = function (key, entry) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.set(key, entry);\n      return temp;\n    });\n  };\n  var setAll = function (newMap) {\n    setMap(new Map(newMap));\n  };\n  var remove = function (key) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.delete(key);\n      return temp;\n    });\n  };\n  var reset = function () {\n    return setMap(getInitValue());\n  };\n  var get = function (key) {\n    return map.get(key);\n  };\n  return [map, {\n    set: useMemoizedFn(set),\n    setAll: useMemoizedFn(setAll),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset),\n    get: useMemoizedFn(get)\n  }];\n}\nexport default useMap;", "import { __read } from \"tslib\";\nimport { useCallback, useRef, useState } from 'react';\nimport useUnmount from '../useUnmount';\nfunction useRafState(initialState) {\n  var ref = useRef(0);\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setRafState = useCallback(function (value) {\n    cancelAnimationFrame(ref.current);\n    ref.current = requestAnimationFrame(function () {\n      setState(value);\n    });\n  }, []);\n  useUnmount(function () {\n    cancelAnimationFrame(ref.current);\n  });\n  return [state, setRafState];\n}\nexport default useRafState;", "import { __read } from \"tslib\";\nimport useRafState from '../useRafState';\nimport useEventListener from '../useEventListener';\nimport { getTargetElement } from '../utils/domTarget';\nvar initState = {\n  screenX: NaN,\n  screenY: NaN,\n  clientX: NaN,\n  clientY: NaN,\n  pageX: NaN,\n  pageY: NaN,\n  elementX: NaN,\n  elementY: NaN,\n  elementH: NaN,\n  elementW: NaN,\n  elementPosX: NaN,\n  elementPosY: NaN\n};\nexport default (function (target) {\n  var _a = __read(useRafState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEventListener('mousemove', function (event) {\n    var screenX = event.screenX,\n      screenY = event.screenY,\n      clientX = event.clientX,\n      clientY = event.clientY,\n      pageX = event.pageX,\n      pageY = event.pageY;\n    var newState = {\n      screenX: screenX,\n      screenY: screenY,\n      clientX: clientX,\n      clientY: clientY,\n      pageX: pageX,\n      pageY: pageY,\n      elementX: NaN,\n      elementY: NaN,\n      elementH: NaN,\n      elementW: NaN,\n      elementPosX: NaN,\n      elementPosY: NaN\n    };\n    var targetElement = getTargetElement(target);\n    if (targetElement) {\n      var _a = targetElement.getBoundingClientRect(),\n        left = _a.left,\n        top_1 = _a.top,\n        width = _a.width,\n        height = _a.height;\n      newState.elementPosX = left + window.pageXOffset;\n      newState.elementPosY = top_1 + window.pageYOffset;\n      newState.elementX = pageX - newState.elementPosX;\n      newState.elementY = pageY - newState.elementPosY;\n      newState.elementW = width;\n      newState.elementH = height;\n    }\n    setState(newState);\n  }, {\n    target: function () {\n      return document;\n    }\n  });\n  return state;\n});", "import { __assign, __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport { isObject } from '../utils';\nvar NetworkEventType;\n(function (NetworkEventType) {\n  NetworkEventType[\"ONLINE\"] = \"online\";\n  NetworkEventType[\"OFFLINE\"] = \"offline\";\n  NetworkEventType[\"CHANGE\"] = \"change\";\n})(NetworkEventType || (NetworkEventType = {}));\nfunction getConnection() {\n  var nav = navigator;\n  if (!isObject(nav)) {\n    return null;\n  }\n  return nav.connection || nav.mozConnection || nav.webkitConnection;\n}\nfunction getConnectionProperty() {\n  var c = getConnection();\n  if (!c) {\n    return {};\n  }\n  return {\n    rtt: c.rtt,\n    type: c.type,\n    saveData: c.saveData,\n    downlink: c.downlink,\n    downlinkMax: c.downlinkMax,\n    effectiveType: c.effectiveType\n  };\n}\nfunction useNetwork() {\n  var _a = __read(useState(function () {\n      return __assign({\n        since: undefined,\n        online: navigator === null || navigator === void 0 ? void 0 : navigator.onLine\n      }, getConnectionProperty());\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    var onOnline = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: true,\n          since: new Date()\n        });\n      });\n    };\n    var onOffline = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: false,\n          since: new Date()\n        });\n      });\n    };\n    var onConnectionChange = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), getConnectionProperty());\n      });\n    };\n    window.addEventListener(NetworkEventType.ONLINE, onOnline);\n    window.addEventListener(NetworkEventType.OFFLINE, onOffline);\n    var connection = getConnection();\n    connection === null || connection === void 0 ? void 0 : connection.addEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    return function () {\n      window.removeEventListener(NetworkEventType.ONLINE, onOnline);\n      window.removeEventListener(NetworkEventType.OFFLINE, onOffline);\n      connection === null || connection === void 0 ? void 0 : connection.removeEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    };\n  }, []);\n  return state;\n}\nexport default useNetwork;", "import { useRef } from 'react';\nvar defaultShouldUpdate = function (a, b) {\n  return !Object.is(a, b);\n};\nfunction usePrevious(state, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = defaultShouldUpdate;\n  }\n  var prevRef = useRef(undefined);\n  var curRef = useRef(undefined);\n  if (shouldUpdate(curRef.current, state)) {\n    prevRef.current = curRef.current;\n    curRef.current = state;\n  }\n  return prevRef.current;\n}\nexport default usePrevious;", "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafInterval = function (callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === 'undefined') {\n    return {\n      id: setInterval(callback, delay)\n    };\n  }\n  var start = Date.now();\n  var handle = {\n    id: 0\n  };\n  var loop = function () {\n    var current = Date.now();\n    if (current - start >= delay) {\n      callback();\n      start = Date.now();\n    }\n    handle.id = requestAnimationFrame(loop);\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nvar cancelAnimationFrameIsNotDefined = function (t) {\n  return typeof cancelAnimationFrame === 'undefined';\n};\nvar clearRafInterval = function (handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearInterval(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafInterval(fn, delay, options) {\n  var immediate = options === null || options === void 0 ? void 0 : options.immediate;\n  var fnRef = useLatest(fn);\n  var timerRef = useRef(undefined);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafInterval(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (immediate) {\n      fnRef.current();\n    }\n    timerRef.current = setRafInterval(function () {\n      fnRef.current();\n    }, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n}\nexport default useRafInterval;", "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafTimeout = function (callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === 'undefined') {\n    return {\n      id: setTimeout(callback, delay)\n    };\n  }\n  var handle = {\n    id: 0\n  };\n  var startTime = Date.now();\n  var loop = function () {\n    var current = Date.now();\n    if (current - startTime >= delay) {\n      callback();\n    } else {\n      handle.id = requestAnimationFrame(loop);\n    }\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nvar cancelAnimationFrameIsNotDefined = function (t) {\n  return typeof cancelAnimationFrame === 'undefined';\n};\nvar clearRafTimeout = function (handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearTimeout(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafTimeout(fn, delay) {\n  var fnRef = useLatest(fn);\n  var timerRef = useRef(undefined);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafTimeout(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    timerRef.current = setRafTimeout(function () {\n      fnRef.current();\n    }, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n}\nexport default useRafTimeout;", "import { useRef } from 'react';\nimport isPlainObject from 'lodash/isPlainObject';\nimport useCreation from '../useCreation';\nimport useUpdate from '../useUpdate';\n// k:v 原对象:代理过的对象\nvar proxyMap = new WeakMap();\n// k:v 代理过的对象:原对象\nvar rawMap = new WeakMap();\nfunction observer(initialVal, cb) {\n  var existingProxy = proxyMap.get(initialVal);\n  // 添加缓存 防止重新构建proxy\n  if (existingProxy) {\n    return existingProxy;\n  }\n  // 防止代理已经代理过的对象\n  // https://github.com/alibaba/hooks/issues/839\n  if (rawMap.has(initialVal)) {\n    return initialVal;\n  }\n  var proxy = new Proxy(initialVal, {\n    get: function (target, key, receiver) {\n      var res = Reflect.get(target, key, receiver);\n      // https://github.com/alibaba/hooks/issues/1317\n      var descriptor = Reflect.getOwnPropertyDescriptor(target, key);\n      if (!(descriptor === null || descriptor === void 0 ? void 0 : descriptor.configurable) && !(descriptor === null || descriptor === void 0 ? void 0 : descriptor.writable)) {\n        return res;\n      }\n      // Only proxy plain object or array,\n      // otherwise it will cause: https://github.com/alibaba/hooks/issues/2080\n      return isPlainObject(res) || Array.isArray(res) ? observer(res, cb) : res;\n    },\n    set: function (target, key, val) {\n      var ret = Reflect.set(target, key, val);\n      cb();\n      return ret;\n    },\n    deleteProperty: function (target, key) {\n      var ret = Reflect.deleteProperty(target, key);\n      cb();\n      return ret;\n    }\n  });\n  proxyMap.set(initialVal, proxy);\n  rawMap.set(proxy, initialVal);\n  return proxy;\n}\nfunction useReactive(initialState) {\n  var update = useUpdate();\n  var stateRef = useRef(initialState);\n  var state = useCreation(function () {\n    return observer(stateRef.current, function () {\n      update();\n    });\n  }, []);\n  return state;\n}\nexport default useReactive;", "import { __read } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useCreation from '../useCreation';\nvar useResetState = function (initialState) {\n  var initialStateRef = useRef(initialState);\n  var initialStateMemo = useCreation(function () {\n    return isFunction(initialStateRef.current) ? initialStateRef.current() : initialStateRef.current;\n  }, []);\n  var _a = __read(useState(initialStateMemo), 2),\n    state = _a[0],\n    setState = _a[1];\n  var resetState = useMemoizedFn(function () {\n    setState(initialStateMemo);\n  });\n  return [state, setState, resetState];\n};\nexport default useResetState;", "import { __read, __values } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar subscribers = new Set();\nvar info;\nvar responsiveConfig = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nfunction handleResize() {\n  var e_1, _a;\n  var oldInfo = info;\n  calculate();\n  if (oldInfo === info) {\n    return;\n  }\n  try {\n    for (var subscribers_1 = __values(subscribers), subscribers_1_1 = subscribers_1.next(); !subscribers_1_1.done; subscribers_1_1 = subscribers_1.next()) {\n      var subscriber = subscribers_1_1.value;\n      subscriber();\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (subscribers_1_1 && !subscribers_1_1.done && (_a = subscribers_1.return)) _a.call(subscribers_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n}\nvar listening = false;\nfunction calculate() {\n  var e_2, _a;\n  var width = window.innerWidth;\n  var newInfo = {};\n  var shouldUpdate = false;\n  try {\n    for (var _b = __values(Object.keys(responsiveConfig)), _c = _b.next(); !_c.done; _c = _b.next()) {\n      var key = _c.value;\n      newInfo[key] = width >= responsiveConfig[key];\n      if (newInfo[key] !== info[key]) {\n        shouldUpdate = true;\n      }\n    }\n  } catch (e_2_1) {\n    e_2 = {\n      error: e_2_1\n    };\n  } finally {\n    try {\n      if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n    } finally {\n      if (e_2) throw e_2.error;\n    }\n  }\n  if (shouldUpdate) {\n    info = newInfo;\n  }\n}\nexport function configResponsive(config) {\n  responsiveConfig = config;\n  if (info) calculate();\n}\nfunction useResponsive() {\n  if (isBrowser && !listening) {\n    info = {};\n    calculate();\n    window.addEventListener('resize', handleResize);\n    listening = true;\n  }\n  var _a = __read(useState(info), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    if (!isBrowser) {\n      return;\n    }\n    // In React 18's StrictMode, useEffect perform twice, resize listener is remove, so handleResize is never perform.\n    // https://github.com/alibaba/hooks/issues/1910\n    if (!listening) {\n      window.addEventListener('resize', handleResize);\n    }\n    var subscriber = function () {\n      setState(info);\n    };\n    subscribers.add(subscriber);\n    return function () {\n      subscribers.delete(subscriber);\n      if (subscribers.size === 0) {\n        window.removeEventListener('resize', handleResize);\n        listening = false;\n      }\n    };\n  }, []);\n  return state;\n}\nexport default useResponsive;", "import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nimport useUnmountedRef from '../useUnmountedRef';\nfunction useSafeState(initialState) {\n  var unmountedRef = useUnmountedRef();\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setCurrentState = useCallback(function (currentState) {\n    /** if component is unmounted, stop update */\n    if (unmountedRef.current) {\n      return;\n    }\n    setState(currentState);\n  }, []);\n  return [state, setCurrentState];\n}\nexport default useSafeState;", "import { useEffect, useRef } from 'react';\nvar useUnmountedRef = function () {\n  var unmountedRef = useRef(false);\n  useEffect(function () {\n    unmountedRef.current = false;\n    return function () {\n      unmountedRef.current = true;\n    };\n  }, []);\n  return unmountedRef;\n};\nexport default useUnmountedRef;", "import { __read } from \"tslib\";\nimport useRafState from '../useRafState';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useScroll(target, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = function () {\n      return true;\n    };\n  }\n  var _a = __read(useRafState(), 2),\n    position = _a[0],\n    setPosition = _a[1];\n  var shouldUpdateRef = useLatest(shouldUpdate);\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var updatePosition = function () {\n      var newPosition;\n      if (el === document) {\n        if (document.scrollingElement) {\n          newPosition = {\n            left: document.scrollingElement.scrollLeft,\n            top: document.scrollingElement.scrollTop\n          };\n        } else {\n          // When in quirks mode, the scrollingElement attribute returns the HTML body element if it exists and is potentially scrollable, otherwise it returns null.\n          // https://developer.mozilla.org/zh-CN/docs/Web/API/Document/scrollingElement\n          // https://stackoverflow.com/questions/28633221/document-body-scrolltop-firefox-returns-0-only-js\n          newPosition = {\n            left: Math.max(window.pageXOffset, document.documentElement.scrollLeft, document.body.scrollLeft),\n            top: Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop)\n          };\n        }\n      } else {\n        newPosition = {\n          left: el.scrollLeft,\n          top: el.scrollTop\n        };\n      }\n      if (shouldUpdateRef.current(newPosition)) {\n        setPosition(newPosition);\n      }\n    };\n    updatePosition();\n    el.addEventListener('scroll', updatePosition);\n    return function () {\n      el.removeEventListener('scroll', updatePosition);\n    };\n  }, [], target);\n  return position;\n}\nexport default useScroll;", "import { __read } from \"tslib\";\nimport isPlainObject from 'lodash/isPlainObject';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nimport { useMemo, useState } from 'react';\nfunction useSelections(items, options) {\n  var _a, _b;\n  var defaultSelected = [];\n  var itemKey;\n  if (Array.isArray(options)) {\n    defaultSelected = options;\n  } else if (isPlainObject(options)) {\n    defaultSelected = (_a = options === null || options === void 0 ? void 0 : options.defaultSelected) !== null && _a !== void 0 ? _a : defaultSelected;\n    itemKey = (_b = options === null || options === void 0 ? void 0 : options.itemKey) !== null && _b !== void 0 ? _b : itemKey;\n  }\n  var getKey = function (item) {\n    if (isFunction(itemKey)) {\n      return itemKey(item);\n    }\n    if (isString(itemKey) && isPlainObject(item)) {\n      return item[itemKey];\n    }\n    return item;\n  };\n  var _c = __read(useState(defaultSelected), 2),\n    selected = _c[0],\n    setSelected = _c[1];\n  var selectedMap = useMemo(function () {\n    var keyToItemMap = new Map();\n    if (!Array.isArray(selected)) {\n      return keyToItemMap;\n    }\n    selected.forEach(function (item) {\n      keyToItemMap.set(getKey(item), item);\n    });\n    return keyToItemMap;\n  }, [selected]);\n  var isSelected = function (item) {\n    return selectedMap.has(getKey(item));\n  };\n  var select = function (item) {\n    selectedMap.set(getKey(item), item);\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var unSelect = function (item) {\n    selectedMap.delete(getKey(item));\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var toggle = function (item) {\n    if (isSelected(item)) {\n      unSelect(item);\n    } else {\n      select(item);\n    }\n  };\n  var selectAll = function () {\n    items.forEach(function (item) {\n      selectedMap.set(getKey(item), item);\n    });\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var unSelectAll = function () {\n    items.forEach(function (item) {\n      selectedMap.delete(getKey(item));\n    });\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var noneSelected = useMemo(function () {\n    return items.every(function (item) {\n      return !selectedMap.has(getKey(item));\n    });\n  }, [items, selectedMap]);\n  var allSelected = useMemo(function () {\n    return items.every(function (item) {\n      return selectedMap.has(getKey(item));\n    }) && !noneSelected;\n  }, [items, selectedMap, noneSelected]);\n  var partiallySelected = useMemo(function () {\n    return !noneSelected && !allSelected;\n  }, [noneSelected, allSelected]);\n  var toggleAll = function () {\n    return allSelected ? unSelectAll() : selectAll();\n  };\n  var clearAll = function () {\n    selectedMap.clear();\n    setSelected([]);\n  };\n  return {\n    selected: selected,\n    noneSelected: noneSelected,\n    allSelected: allSelected,\n    partiallySelected: partiallySelected,\n    setSelected: setSelected,\n    isSelected: isSelected,\n    select: useMemoizedFn(select),\n    unSelect: useMemoizedFn(unSelect),\n    toggle: useMemoizedFn(toggle),\n    selectAll: useMemoizedFn(selectAll),\n    unSelectAll: useMemoizedFn(unSelectAll),\n    clearAll: useMemoizedFn(clearAll),\n    toggleAll: useMemoizedFn(toggleAll)\n  };\n}\nexport default useSelections;", "import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useSessionStorageState = createUseStorageState(function () {\n  return isBrowser ? sessionStorage : undefined;\n});\nexport default useSessionStorageState;", "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useSet(initialValue) {\n  var getInitValue = function () {\n    return new Set(initialValue);\n  };\n  var _a = __read(useState(getInitValue), 2),\n    set = _a[0],\n    setSet = _a[1];\n  var add = function (key) {\n    if (set.has(key)) {\n      return;\n    }\n    setSet(function (prevSet) {\n      var temp = new Set(prevSet);\n      temp.add(key);\n      return temp;\n    });\n  };\n  var remove = function (key) {\n    if (!set.has(key)) {\n      return;\n    }\n    setSet(function (prevSet) {\n      var temp = new Set(prevSet);\n      temp.delete(key);\n      return temp;\n    });\n  };\n  var reset = function () {\n    return setSet(getInitValue());\n  };\n  return [set, {\n    add: useMemoizedFn(add),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useSet;", "import { __assign, __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction } from '../utils';\nvar useSetState = function (initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setMergeState = useMemoizedFn(function (patch) {\n    setState(function (prevState) {\n      var newState = isFunction(patch) ? patch(prevState) : patch;\n      return newState ? __assign(__assign({}, prevState), newState) : prevState;\n    });\n  });\n  return [state, setMergeState];\n};\nexport default useSetState;", "import { useLayoutEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useLayoutEffect);\nexport default useEffectWithTarget;", "import isBrowser from './isBrowser';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport useLayoutEffectWithTarget from './useLayoutEffectWithTarget';\nvar useIsomorphicLayoutEffectWithTarget = isBrowser ? useLayoutEffectWithTarget : useEffectWithTarget;\nexport default useIsomorphicLayoutEffectWithTarget;", "import { __read } from \"tslib\";\nimport ResizeObserver from 'resize-observer-polyfill';\nimport useRafState from '../useRafState';\nimport { getTargetElement } from '../utils/domTarget';\nimport useIsomorphicLayoutEffectWithTarget from '../utils/useIsomorphicLayoutEffectWithTarget';\nfunction useSize(target) {\n  var _a = __read(useRafState(function () {\n      var el = getTargetElement(target);\n      return el ? {\n        width: el.clientWidth,\n        height: el.clientHeight\n      } : undefined;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  useIsomorphicLayoutEffectWithTarget(function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var resizeObserver = new ResizeObserver(function (entries) {\n      entries.forEach(function (entry) {\n        var _a = entry.target,\n          clientWidth = _a.clientWidth,\n          clientHeight = _a.clientHeight;\n        setState({\n          width: clientWidth,\n          height: clientHeight\n        });\n      });\n    });\n    resizeObserver.observe(el);\n    return function () {\n      resizeObserver.disconnect();\n    };\n  }, [], target);\n  return state;\n}\nexport default useSize;", "import { __assign, __read } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar initRect = {\n  top: NaN,\n  left: NaN,\n  bottom: NaN,\n  right: NaN,\n  height: NaN,\n  width: NaN\n};\nvar initState = __assign({\n  text: ''\n}, initRect);\nfunction getRectFromSelection(selection) {\n  if (!selection) {\n    return initRect;\n  }\n  if (selection.rangeCount < 1) {\n    return initRect;\n  }\n  var range = selection.getRangeAt(0);\n  var _a = range.getBoundingClientRect(),\n    height = _a.height,\n    width = _a.width,\n    top = _a.top,\n    left = _a.left,\n    right = _a.right,\n    bottom = _a.bottom;\n  return {\n    height: height,\n    width: width,\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  };\n}\nfunction useTextSelection(target) {\n  var _a = __read(useState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useRef(state);\n  var isInRangeRef = useRef(false);\n  stateRef.current = state;\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var mouseupHandler = function () {\n      var selObj = null;\n      var text = '';\n      var rect = initRect;\n      if (!window.getSelection) {\n        return;\n      }\n      selObj = window.getSelection();\n      text = selObj ? selObj.toString() : '';\n      if (text && isInRangeRef.current) {\n        rect = getRectFromSelection(selObj);\n        setState(__assign(__assign(__assign({}, state), {\n          text: text\n        }), rect));\n      }\n    };\n    // 任意点击都需要清空之前的 range\n    var mousedownHandler = function (e) {\n      // 如果是鼠标右键需要跳过 这样选中的数据就不会被清空\n      if (e.button === 2) {\n        return;\n      }\n      if (!window.getSelection) {\n        return;\n      }\n      if (stateRef.current.text) {\n        setState(__assign({}, initState));\n      }\n      isInRangeRef.current = false;\n      var selObj = window.getSelection();\n      if (!selObj) {\n        return;\n      }\n      selObj.removeAllRanges();\n      isInRangeRef.current = el.contains(e.target);\n    };\n    el.addEventListener('mouseup', mouseupHandler);\n    document.addEventListener('mousedown', mousedownHandler);\n    return function () {\n      el.removeEventListener('mouseup', mouseupHandler);\n      document.removeEventListener('mousedown', mousedownHandler);\n    };\n  }, [], target);\n  return state;\n}\nexport default useTextSelection;", "import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useThrottleFn from '../useThrottleFn';\nfunction useThrottle(value, options) {\n  var _a = __read(useState(value), 2),\n    throttled = _a[0],\n    setThrottled = _a[1];\n  var run = useThrottleFn(function () {\n    setThrottled(value);\n  }, options).run;\n  useEffect(function () {\n    run();\n  }, [value]);\n  return throttled;\n}\nexport default useThrottle;", "import { __read, __spreadArray } from \"tslib\";\nimport throttle from 'lodash/throttle';\nimport { useMemo } from 'react';\nimport useLatest from '../useLatest';\nimport useUnmount from '../useUnmount';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useThrottleFn(fn, options) {\n  var _a;\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useThrottleFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;\n  var throttled = useMemo(function () {\n    return throttle(function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));\n    }, wait, options);\n  }, []);\n  useUnmount(function () {\n    throttled.cancel();\n  });\n  return {\n    run: throttled,\n    cancel: throttled.cancel,\n    flush: throttled.flush\n  };\n}\nexport default useThrottleFn;", "import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useThrottleFn from '../useThrottleFn';\nimport useUpdateEffect from '../useUpdateEffect';\nfunction useThrottleEffect(effect, deps, options) {\n  var _a = __read(useState({}), 2),\n    flag = _a[0],\n    setFlag = _a[1];\n  var run = useThrottleFn(function () {\n    setFlag({});\n  }, options).run;\n  useEffect(function () {\n    return run();\n  }, deps);\n  useUpdateEffect(effect, [flag]);\n}\nexport default useThrottleEffect;", "import { useCallback, useEffect, useRef } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar useTimeout = function (fn, delay) {\n  var timerCallback = useMemoizedFn(fn);\n  var timerRef = useRef(null);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    timerRef.current = setTimeout(timerCallback, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n};\nexport default useTimeout;", "import { useEffect, useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport isBrowser from '../utils/isBrowser';\nvar DEFAULT_OPTIONS = {\n  restoreOnUnmount: false\n};\nfunction useTitle(title, options) {\n  if (options === void 0) {\n    options = DEFAULT_OPTIONS;\n  }\n  var titleRef = useRef(isBrowser ? document.title : '');\n  useEffect(function () {\n    document.title = title;\n  }, [title]);\n  useUnmount(function () {\n    if (options.restoreOnUnmount) {\n      document.title = titleRef.current;\n    }\n  });\n}\nexport default useTitle;", "import { useEffect, useRef } from 'react';\nvar diffTwoDeps = function (deps1, deps2) {\n  // Let's do a reference equality check on 2 dependency list.\n  // If deps1 is defined, we iterate over deps1 and do comparison on each element with equivalent element from deps2\n  // As this func is used only in this hook, we assume 2 deps always have same length.\n  return deps1 ? deps1.map(function (_, idx) {\n    return !Object.is(deps1[idx], deps2 === null || deps2 === void 0 ? void 0 : deps2[idx]) ? idx : -1;\n  }).filter(function (ele) {\n    return ele >= 0;\n  }) : deps2 ? deps2.map(function (_, idx) {\n    return idx;\n  }) : [];\n};\nvar useTrackedEffect = function (effect, deps) {\n  var previousDepsRef = useRef(undefined);\n  useEffect(function () {\n    var changes = diffTwoDeps(previousDepsRef.current, deps);\n    var previousDeps = previousDepsRef.current;\n    previousDepsRef.current = deps;\n    return effect(changes, previousDeps, deps);\n  }, deps);\n};\nexport default useTrackedEffect;", "import { useLayoutEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useLayoutEffect);", "import { __read } from \"tslib\";\nimport { useEffect, useMemo, useState, useRef } from 'react';\nimport useEventListener from '../useEventListener';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useSize from '../useSize';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isNumber } from '../utils';\nimport useUpdateEffect from '../useUpdateEffect';\nvar useVirtualList = function (list, options) {\n  var containerTarget = options.containerTarget,\n    wrapperTarget = options.wrapperTarget,\n    itemHeight = options.itemHeight,\n    _a = options.overscan,\n    overscan = _a === void 0 ? 5 : _a;\n  var itemHeightRef = useLatest(itemHeight);\n  var size = useSize(containerTarget);\n  var scrollTriggerByScrollToFunc = useRef(false);\n  var _b = __read(useState([]), 2),\n    targetList = _b[0],\n    setTargetList = _b[1];\n  var _c = __read(useState({}), 2),\n    wrapperStyle = _c[0],\n    setWrapperStyle = _c[1];\n  var getVisibleCount = function (containerHeight, fromIndex) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.ceil(containerHeight / itemHeightRef.current);\n    }\n    var sum = 0;\n    var endIndex = 0;\n    for (var i = fromIndex; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      endIndex = i;\n      if (sum >= containerHeight) {\n        break;\n      }\n    }\n    return endIndex - fromIndex;\n  };\n  var getOffset = function (scrollTop) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.floor(scrollTop / itemHeightRef.current);\n    }\n    var sum = 0;\n    var offset = 0;\n    for (var i = 0; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      if (sum >= scrollTop) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n  // 获取上部高度\n  var getDistanceTop = function (index) {\n    if (isNumber(itemHeightRef.current)) {\n      var height_1 = index * itemHeightRef.current;\n      return height_1;\n    }\n    var height = list.slice(0, index).reduce(function (sum, _, i) {\n      return sum + itemHeightRef.current(i, list[i]);\n    }, 0);\n    return height;\n  };\n  var totalHeight = useMemo(function () {\n    if (isNumber(itemHeightRef.current)) {\n      return list.length * itemHeightRef.current;\n    }\n    return list.reduce(function (sum, _, index) {\n      return sum + itemHeightRef.current(index, list[index]);\n    }, 0);\n  }, [list]);\n  var calculateRange = function () {\n    var container = getTargetElement(containerTarget);\n    if (container) {\n      var scrollTop = container.scrollTop,\n        clientHeight = container.clientHeight;\n      var offset = getOffset(scrollTop);\n      var visibleCount = getVisibleCount(clientHeight, offset);\n      var start_1 = Math.max(0, offset - overscan);\n      var end = Math.min(list.length, offset + visibleCount + overscan);\n      var offsetTop = getDistanceTop(start_1);\n      setWrapperStyle({\n        height: totalHeight - offsetTop + 'px',\n        marginTop: offsetTop + 'px'\n      });\n      setTargetList(list.slice(start_1, end).map(function (ele, index) {\n        return {\n          data: ele,\n          index: index + start_1\n        };\n      }));\n    }\n  };\n  useUpdateEffect(function () {\n    var wrapper = getTargetElement(wrapperTarget);\n    if (wrapper) {\n      Object.keys(wrapperStyle).forEach(function (key) {\n        return wrapper.style[key] = wrapperStyle[key];\n      });\n    }\n  }, [wrapperStyle]);\n  useEffect(function () {\n    if (!(size === null || size === void 0 ? void 0 : size.width) || !(size === null || size === void 0 ? void 0 : size.height)) {\n      return;\n    }\n    calculateRange();\n  }, [size === null || size === void 0 ? void 0 : size.width, size === null || size === void 0 ? void 0 : size.height, list]);\n  useEventListener('scroll', function (e) {\n    if (scrollTriggerByScrollToFunc.current) {\n      scrollTriggerByScrollToFunc.current = false;\n      return;\n    }\n    e.preventDefault();\n    calculateRange();\n  }, {\n    target: containerTarget\n  });\n  var scrollTo = function (index) {\n    var container = getTargetElement(containerTarget);\n    if (container) {\n      scrollTriggerByScrollToFunc.current = true;\n      container.scrollTop = getDistanceTop(index);\n      calculateRange();\n    }\n  };\n  return [targetList, useMemoizedFn(scrollTo)];\n};\nexport default useVirtualList;", "import { __read } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUnmount from '../useUnmount';\nexport var ReadyState;\n(function (ReadyState) {\n  ReadyState[ReadyState[\"Connecting\"] = 0] = \"Connecting\";\n  ReadyState[ReadyState[\"Open\"] = 1] = \"Open\";\n  ReadyState[ReadyState[\"Closing\"] = 2] = \"Closing\";\n  ReadyState[ReadyState[\"Closed\"] = 3] = \"Closed\";\n})(ReadyState || (ReadyState = {}));\nfunction useWebSocket(socketUrl, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.reconnectLimit,\n    reconnectLimit = _a === void 0 ? 3 : _a,\n    _b = options.reconnectInterval,\n    reconnectInterval = _b === void 0 ? 3 * 1000 : _b,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    onOpen = options.onOpen,\n    onClose = options.onClose,\n    onMessage = options.onMessage,\n    onError = options.onError,\n    protocols = options.protocols;\n  var onOpenRef = useLatest(onOpen);\n  var onCloseRef = useLatest(onClose);\n  var onMessageRef = useLatest(onMessage);\n  var onErrorRef = useLatest(onError);\n  var reconnectTimesRef = useRef(0);\n  var reconnectTimerRef = useRef(undefined);\n  var websocketRef = useRef(undefined);\n  var _d = __read(useState(), 2),\n    latestMessage = _d[0],\n    setLatestMessage = _d[1];\n  var _e = __read(useState(ReadyState.Closed), 2),\n    readyState = _e[0],\n    setReadyState = _e[1];\n  var reconnect = function () {\n    var _a;\n    if (reconnectTimesRef.current < reconnectLimit && ((_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.readyState) !== ReadyState.Open) {\n      if (reconnectTimerRef.current) {\n        clearTimeout(reconnectTimerRef.current);\n      }\n      reconnectTimerRef.current = setTimeout(function () {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        connectWs();\n        reconnectTimesRef.current++;\n      }, reconnectInterval);\n    }\n  };\n  var connectWs = function () {\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    if (websocketRef.current) {\n      websocketRef.current.close();\n    }\n    var ws = new WebSocket(socketUrl, protocols);\n    setReadyState(ReadyState.Connecting);\n    ws.onerror = function (event) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      reconnect();\n      (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, event, ws);\n      setReadyState(ws.readyState || ReadyState.Closed);\n    };\n    ws.onopen = function (event) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      (_a = onOpenRef.current) === null || _a === void 0 ? void 0 : _a.call(onOpenRef, event, ws);\n      reconnectTimesRef.current = 0;\n      setReadyState(ws.readyState || ReadyState.Open);\n    };\n    ws.onmessage = function (message) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      (_a = onMessageRef.current) === null || _a === void 0 ? void 0 : _a.call(onMessageRef, message, ws);\n      setLatestMessage(message);\n    };\n    ws.onclose = function (event) {\n      var _a;\n      (_a = onCloseRef.current) === null || _a === void 0 ? void 0 : _a.call(onCloseRef, event, ws);\n      // closed by server\n      if (websocketRef.current === ws) {\n        reconnect();\n      }\n      // closed by disconnect or closed by server\n      if (!websocketRef.current || websocketRef.current === ws) {\n        setReadyState(ws.readyState || ReadyState.Closed);\n      }\n    };\n    websocketRef.current = ws;\n  };\n  var sendMessage = function (message) {\n    var _a;\n    if (readyState === ReadyState.Open) {\n      (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.send(message);\n    } else {\n      throw new Error('WebSocket disconnected');\n    }\n  };\n  var connect = function () {\n    reconnectTimesRef.current = 0;\n    connectWs();\n  };\n  var disconnect = function () {\n    var _a;\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    reconnectTimesRef.current = reconnectLimit;\n    (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.close();\n    websocketRef.current = undefined;\n  };\n  useEffect(function () {\n    if (!manual && socketUrl) {\n      connect();\n    }\n  }, [socketUrl, manual]);\n  useUnmount(function () {\n    disconnect();\n  });\n  return {\n    latestMessage: latestMessage,\n    sendMessage: useMemoizedFn(sendMessage),\n    connect: useMemoizedFn(connect),\n    disconnect: useMemoizedFn(disconnect),\n    readyState: readyState,\n    webSocketIns: websocketRef.current\n  };\n}\nexport default useWebSocket;", "import { __assign } from \"tslib\";\nimport { useEffect, useRef } from 'react';\nfunction useWhyDidYouUpdate(componentName, props) {\n  var prevProps = useRef({});\n  useEffect(function () {\n    if (prevProps.current) {\n      var allKeys = Object.keys(__assign(__assign({}, prevProps.current), props));\n      var changedProps_1 = {};\n      allKeys.forEach(function (key) {\n        if (!Object.is(prevProps.current[key], props[key])) {\n          changedProps_1[key] = {\n            from: prevProps.current[key],\n            to: props[key]\n          };\n        }\n      });\n      if (Object.keys(changedProps_1).length) {\n        console.log('[why-did-you-update]', componentName, changedProps_1);\n      }\n    }\n    prevProps.current = props;\n  });\n}\nexport default useWhyDidYouUpdate;", "import { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport useLatest from '../useLatest';\nvar useMutationObserver = function (callback, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var callbackRef = useLatest(callback);\n  useDeepCompareEffectWithTarget(function () {\n    var element = getTargetElement(target);\n    if (!element) {\n      return;\n    }\n    var observer = new MutationObserver(callbackRef.current);\n    observer.observe(element, options);\n    return function () {\n      observer === null || observer === void 0 ? void 0 : observer.disconnect();\n    };\n  }, [options], target);\n};\nexport default useMutationObserver;", "import { __read } from \"tslib\";\nimport { useEffect, useState } from \"react\";\nimport useMemoizedFn from \"../useMemoizedFn\";\nimport isBrowser from \"../utils/isBrowser\";\nexport var ThemeMode;\n(function (ThemeMode) {\n  ThemeMode[\"LIGHT\"] = \"light\";\n  ThemeMode[\"DARK\"] = \"dark\";\n  ThemeMode[\"SYSTEM\"] = \"system\";\n})(ThemeMode || (ThemeMode = {}));\nvar useCurrentTheme = function () {\n  var matchMedia = isBrowser ? window.matchMedia(\"(prefers-color-scheme: dark)\") : undefined;\n  var _a = __read(useState(function () {\n      if (isBrowser) {\n        return (matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.matches) ? ThemeMode.DARK : ThemeMode.LIGHT;\n      } else {\n        return ThemeMode.LIGHT;\n      }\n    }), 2),\n    theme = _a[0],\n    setTheme = _a[1];\n  useEffect(function () {\n    var onThemeChange = function (event) {\n      if (event.matches) {\n        setTheme(ThemeMode.DARK);\n      } else {\n        setTheme(ThemeMode.LIGHT);\n      }\n    };\n    matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.addEventListener(\"change\", onThemeChange);\n    return function () {\n      matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.removeEventListener(\"change\", onThemeChange);\n    };\n  }, []);\n  return theme;\n};\nexport default function useTheme(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var localStorageKey = options.localStorageKey;\n  var _a = __read(useState(function () {\n      var preferredThemeMode = (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) && localStorage.getItem(localStorageKey);\n      return preferredThemeMode ? preferredThemeMode : ThemeMode.SYSTEM;\n    }), 2),\n    themeMode = _a[0],\n    setThemeMode = _a[1];\n  var setThemeModeWithLocalStorage = function (mode) {\n    setThemeMode(mode);\n    if (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) {\n      localStorage.setItem(localStorageKey, mode);\n    }\n  };\n  var currentTheme = useCurrentTheme();\n  var theme = themeMode === ThemeMode.SYSTEM ? currentTheme : themeMode;\n  return {\n    theme: theme,\n    themeMode: themeMode,\n    setThemeMode: useMemoizedFn(setThemeModeWithLocalStorage)\n  };\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAIA,YAAW;AAAf,QACI,MAAM;AADV,QAEI,WAAW;AAGf,QAAI,kBAAkB;AAGtB,QAAI,YAAY,KAAK;AAArB,QACI,YAAY,KAAK;AAwDrB,aAASC,UAAS,MAAM,MAAM,SAAS;AACrC,UAAI,UACA,UACA,SACA,QACA,SACA,cACA,iBAAiB,GACjB,UAAU,OACV,SAAS,OACT,WAAW;AAEf,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,eAAe;AAAA,MACrC;AACA,aAAO,SAAS,IAAI,KAAK;AACzB,UAAID,UAAS,OAAO,GAAG;AACrB,kBAAU,CAAC,CAAC,QAAQ;AACpB,iBAAS,aAAa;AACtB,kBAAU,SAAS,UAAU,SAAS,QAAQ,OAAO,KAAK,GAAG,IAAI,IAAI;AACrE,mBAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,MAC1D;AAEA,eAAS,WAAW,MAAM;AACxB,YAAI,OAAO,UACP,UAAU;AAEd,mBAAW,WAAW;AACtB,yBAAiB;AACjB,iBAAS,KAAK,MAAM,SAAS,IAAI;AACjC,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,MAAM;AAEzB,yBAAiB;AAEjB,kBAAU,WAAW,cAAc,IAAI;AAEvC,eAAO,UAAU,WAAW,IAAI,IAAI;AAAA,MACtC;AAEA,eAAS,cAAc,MAAM;AAC3B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO,gBAC7B,cAAc,OAAO;AAEzB,eAAO,SACH,UAAU,aAAa,UAAU,mBAAmB,IACpD;AAAA,MACN;AAEA,eAAS,aAAa,MAAM;AAC1B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO;AAKjC,eAAQ,iBAAiB,UAAc,qBAAqB,QACzD,oBAAoB,KAAO,UAAU,uBAAuB;AAAA,MACjE;AAEA,eAAS,eAAe;AACtB,YAAI,OAAO,IAAI;AACf,YAAI,aAAa,IAAI,GAAG;AACtB,iBAAO,aAAa,IAAI;AAAA,QAC1B;AAEA,kBAAU,WAAW,cAAc,cAAc,IAAI,CAAC;AAAA,MACxD;AAEA,eAAS,aAAa,MAAM;AAC1B,kBAAU;AAIV,YAAI,YAAY,UAAU;AACxB,iBAAO,WAAW,IAAI;AAAA,QACxB;AACA,mBAAW,WAAW;AACtB,eAAO;AAAA,MACT;AAEA,eAAS,SAAS;AAChB,YAAI,YAAY,QAAW;AACzB,uBAAa,OAAO;AAAA,QACtB;AACA,yBAAiB;AACjB,mBAAW,eAAe,WAAW,UAAU;AAAA,MACjD;AAEA,eAAS,QAAQ;AACf,eAAO,YAAY,SAAY,SAAS,aAAa,IAAI,CAAC;AAAA,MAC5D;AAEA,eAAS,YAAY;AACnB,YAAI,OAAO,IAAI,GACX,aAAa,aAAa,IAAI;AAElC,mBAAW;AACX,mBAAW;AACX,uBAAe;AAEf,YAAI,YAAY;AACd,cAAI,YAAY,QAAW;AACzB,mBAAO,YAAY,YAAY;AAAA,UACjC;AACA,cAAI,QAAQ;AAEV,yBAAa,OAAO;AACpB,sBAAU,WAAW,cAAc,IAAI;AACvC,mBAAO,WAAW,YAAY;AAAA,UAChC;AAAA,QACF;AACA,YAAI,YAAY,QAAW;AACzB,oBAAU,WAAW,cAAc,IAAI;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AACnB,gBAAU,QAAQ;AAClB,aAAO;AAAA,IACT;AAEA,WAAO,UAAUC;AAAA;AAAA;;;AC9LjB;AAAA;AAAA,QAAIC,YAAW;AAAf,QACIC,YAAW;AAGf,QAAI,kBAAkB;AA8CtB,aAASC,UAAS,MAAM,MAAM,SAAS;AACrC,UAAI,UAAU,MACV,WAAW;AAEf,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,eAAe;AAAA,MACrC;AACA,UAAID,UAAS,OAAO,GAAG;AACrB,kBAAU,aAAa,UAAU,CAAC,CAAC,QAAQ,UAAU;AACrD,mBAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,MAC1D;AACA,aAAOD,UAAS,MAAM,MAAM;AAAA,QAC1B,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAEA,WAAO,UAAUE;AAAA;AAAA;;;ACpEjB;AAAA;AAEA,QAAI,iBAAiB,OAAO,YAAY;AACxC,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,iBAAiB,OAAO,gBAAgB,cAAc,CAAC,CAAC,YAAY;AAIxE,aAAS,MAAM,GAAG,GAAG;AAEnB,UAAI,MAAM;AAAG,eAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE;AAAa,iBAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE;AAAQ,mBAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,qBAAO;AACjC,iBAAO;AAAA,QACT;AAsBA,YAAI;AACJ,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE;AAAM,mBAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAAG,qBAAO;AACjC,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AAAG,qBAAO;AACpD,iBAAO;AAAA,QACT;AAEA,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE;AAAM,mBAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAAG,qBAAO;AACjC,iBAAO;AAAA,QACT;AAGA,YAAI,kBAAkB,YAAY,OAAO,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG;AACpE,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE;AAAQ,mBAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG,qBAAO;AAC5B,iBAAO;AAAA,QACT;AAEA,YAAI,EAAE,gBAAgB;AAAQ,iBAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAK5E,YAAI,EAAE,YAAY,OAAO,UAAU,WAAW,OAAO,EAAE,YAAY,cAAc,OAAO,EAAE,YAAY;AAAY,iBAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AACnJ,YAAI,EAAE,aAAa,OAAO,UAAU,YAAY,OAAO,EAAE,aAAa,cAAc,OAAO,EAAE,aAAa;AAAY,iBAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAGzJ,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE;AAAQ,iBAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC;AAAG,mBAAO;AAKhE,YAAI,kBAAkB,aAAa;AAAS,iBAAO;AAGnD,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,eAAK,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,MAAM,SAAS,KAAK,CAAC,MAAM,UAAU,EAAE,UAAU;AASlF;AAAA,UACF;AAGA,cAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AAAG,mBAAO;AAAA,QAC7C;AAIA,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,KAAK,MAAM;AAAA,IAC1B;AAGA,WAAO,UAAU,SAASC,SAAQ,GAAG,GAAG;AACtC,UAAI;AACF,eAAO,MAAM,GAAG,CAAC;AAAA,MACnB,SAAS,OAAO;AACd,aAAM,MAAM,WAAW,IAAI,MAAM,kBAAkB,GAAI;AAMrD,kBAAQ,KAAK,gDAAgD;AAC7D,iBAAO;AAAA,QACT;AAEA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;;;AC1IA;AAAA;AAKA,KAAC,WAAY;AACZ;AAEA,UAAIC,YAAW,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,cAAc,OAAO,WAAW,CAAC;AAC5G,UAAI,aAAa,OAAO,WAAW,eAAe,OAAO;AAEzD,UAAI,KAAM,WAAY;AACrB,YAAI;AAEJ,YAAI,QAAQ;AAAA,UACX;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA;AAAA,UAEA;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UAED;AAAA;AAAA,UAEA;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UAED;AAAA,UACA;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACA;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,QACD;AAEA,YAAI,IAAI;AACR,YAAI,IAAI,MAAM;AACd,YAAI,MAAM,CAAC;AAEX,eAAO,IAAI,GAAG,KAAK;AAClB,gBAAM,MAAM,CAAC;AACb,cAAI,OAAO,IAAI,CAAC,KAAKA,WAAU;AAC9B,iBAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAChC,kBAAI,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC;AAAA,YACzB;AACA,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR,EAAG;AAEH,UAAI,eAAe;AAAA,QAClB,QAAQ,GAAG;AAAA,QACX,OAAO,GAAG;AAAA,MACX;AAEA,UAAIC,cAAa;AAAA,QAChB,SAAS,SAAU,SAAS,SAAS;AACpC,iBAAO,IAAI,SAAQ,SAAU,SAAS,QAAQ;AAC7C,gBAAI,uBAAsB,WAAY;AACrC,mBAAK,IAAI,UAAU,mBAAmB;AACtC,sBAAQ;AAAA,YACT,GAAE,KAAK,IAAI;AAEX,iBAAK,GAAG,UAAU,mBAAmB;AAErC,sBAAU,WAAWD,UAAS;AAE9B,gBAAI,gBAAgB,QAAQ,GAAG,iBAAiB,EAAE,OAAO;AAEzD,gBAAI,yBAAyB,SAAS;AACrC,4BAAc,KAAK,mBAAmB,EAAE,MAAM,MAAM;AAAA,YACrD;AAAA,UACD,GAAE,KAAK,IAAI,CAAC;AAAA,QACb;AAAA,QACA,MAAM,WAAY;AACjB,iBAAO,IAAI,SAAQ,SAAU,SAAS,QAAQ;AAC7C,gBAAI,CAAC,KAAK,cAAc;AACvB,sBAAQ;AACR;AAAA,YACD;AAEA,gBAAI,oBAAmB,WAAY;AAClC,mBAAK,IAAI,UAAU,gBAAgB;AACnC,sBAAQ;AAAA,YACT,GAAE,KAAK,IAAI;AAEX,iBAAK,GAAG,UAAU,gBAAgB;AAElC,gBAAI,gBAAgBA,UAAS,GAAG,cAAc,EAAE;AAEhD,gBAAI,yBAAyB,SAAS;AACrC,4BAAc,KAAK,gBAAgB,EAAE,MAAM,MAAM;AAAA,YAClD;AAAA,UACD,GAAE,KAAK,IAAI,CAAC;AAAA,QACb;AAAA,QACA,QAAQ,SAAU,SAAS,SAAS;AACnC,iBAAO,KAAK,eAAe,KAAK,KAAK,IAAI,KAAK,QAAQ,SAAS,OAAO;AAAA,QACvE;AAAA,QACA,UAAU,SAAU,UAAU;AAC7B,eAAK,GAAG,UAAU,QAAQ;AAAA,QAC3B;AAAA,QACA,SAAS,SAAU,UAAU;AAC5B,eAAK,GAAG,SAAS,QAAQ;AAAA,QAC1B;AAAA,QACA,IAAI,SAAU,OAAO,UAAU;AAC9B,cAAI,YAAY,aAAa,KAAK;AAClC,cAAI,WAAW;AACd,YAAAA,UAAS,iBAAiB,WAAW,UAAU,KAAK;AAAA,UACrD;AAAA,QACD;AAAA,QACA,KAAK,SAAU,OAAO,UAAU;AAC/B,cAAI,YAAY,aAAa,KAAK;AAClC,cAAI,WAAW;AACd,YAAAA,UAAS,oBAAoB,WAAW,UAAU,KAAK;AAAA,UACxD;AAAA,QACD;AAAA,QACA,KAAK;AAAA,MACN;AAEA,UAAI,CAAC,IAAI;AACR,YAAI,YAAY;AACf,iBAAO,UAAU,EAAC,WAAW,MAAK;AAAA,QACnC,OAAO;AACN,iBAAO,aAAa,EAAC,WAAW,MAAK;AAAA,QACtC;AAEA;AAAA,MACD;AAEA,aAAO,iBAAiBC,aAAY;AAAA,QACnC,cAAc;AAAA,UACb,KAAK,WAAY;AAChB,mBAAO,QAAQD,UAAS,GAAG,iBAAiB,CAAC;AAAA,UAC9C;AAAA,QACD;AAAA,QACA,SAAS;AAAA,UACR,YAAY;AAAA,UACZ,KAAK,WAAY;AAChB,mBAAOA,UAAS,GAAG,iBAAiB;AAAA,UACrC;AAAA,QACD;AAAA,QACA,WAAW;AAAA,UACV,YAAY;AAAA,UACZ,KAAK,WAAY;AAEhB,mBAAO,QAAQA,UAAS,GAAG,iBAAiB,CAAC;AAAA,UAC9C;AAAA,QACD;AAAA,MACD,CAAC;AAED,UAAI,YAAY;AACf,eAAO,UAAUC;AAAA,MAClB,OAAO;AACN,eAAO,aAAaA;AAAA,MACrB;AAAA,IACD,GAAG;AAAA;AAAA;;;ACvLH,mBAAuB;AAChB,IAAI,qBAAqB,SAAU,MAAM;AAC9C,SAAO,SAAU,QAAQ,MAAM;AAC7B,QAAI,gBAAY,qBAAO,KAAK;AAE5B,SAAK,WAAY;AACf,aAAO,WAAY;AACjB,kBAAU,UAAU;AAAA,MACtB;AAAA,IACF,GAAG,CAAC,CAAC;AACL,SAAK,WAAY;AACf,UAAI,CAAC,UAAU,SAAS;AACtB,kBAAU,UAAU;AAAA,MACtB,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF,GAAG,IAAI;AAAA,EACT;AACF;;;ACjBA,IAAAC,iBAA4C;;;ACD5C,IAAAC,gBAAgC;;;ACAzB,IAAI,WAAW,SAAU,OAAO;AACrC,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC5C;AACO,IAAI,aAAa,SAAU,OAAO;AACvC,SAAO,OAAO,UAAU;AAC1B;AACO,IAAI,WAAW,SAAU,OAAO;AACrC,SAAO,OAAO,UAAU;AAC1B;AACO,IAAI,YAAY,SAAU,OAAO;AACtC,SAAO,OAAO,UAAU;AAC1B;AACO,IAAI,WAAW,SAAU,OAAO;AACrC,SAAO,OAAO,UAAU;AAC1B;AACO,IAAI,UAAU,SAAU,OAAO;AACpC,SAAO,OAAO,UAAU;AAC1B;;;ACjBA,IAAI,QAAQ;AACZ,IAAO,gBAAQ;;;AFEf,SAAS,cAAc,IAAI;AACzB,MAAI,eAAO;AACT,QAAI,CAAC,WAAW,EAAE,GAAG;AACnB,cAAQ,MAAM,uDAAuD,OAAO,OAAO,EAAE,CAAC;AAAA,IACxF;AAAA,EACF;AACA,MAAI,YAAQ,sBAAO,EAAE;AAGrB,QAAM,cAAU,uBAAQ,WAAY;AAClC,WAAO;AAAA,EACT,GAAG,CAAC,EAAE,CAAC;AACP,MAAI,iBAAa,sBAAO,MAAS;AACjC,MAAI,CAAC,WAAW,SAAS;AACvB,eAAW,UAAU,WAAY;AAC/B,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MACzB;AACA,aAAO,MAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,IACvC;AAAA,EACF;AACA,SAAO,WAAW;AACpB;AACA,IAAO,wBAAQ;;;AG1Bf,IAAAC,iBAAwB;;;ACAxB,IAAAC,gBAAuB;;;ACDvB,IAAAC,gBAA0B;AAE1B,IAAO,0BAAQ,mBAAmB,uBAAS;;;ADE3C,IAAI,mBAAmB,SAAU,eAAe,IAAI;AAClD,MAAI,SAAS,GAAG,QACd,KAAK,GAAG,OACR,QAAQ,OAAO,SAAS,OAAO,IAC/B,KAAK,GAAG,eACR,gBAAgB,OAAO,SAAS,CAAC,IAAI,IACrC,KAAK,GAAG,aACR,cAAc,OAAO,SAAS,CAAC,IAAI,IACnC,oBAAoB,GAAG;AACzB,MAAI,iBAAa,sBAAO,KAAK;AAC7B,aAAW,UAAU;AACrB,0BAAgB,WAAY;AAC1B,QAAI,CAAC,UAAU,OAAO;AACpB,iBAAW,UAAU;AACrB,oBAAc,IAAI,MAAM,eAAe,cAAc,CAAC,GAAG,OAAO,aAAa,GAAG,KAAK,CAAC;AAAA,IACxF;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,0BAAgB,WAAY;AAC1B,QAAI,WAAW,SAAS;AACtB;AAAA,IACF;AACA,QAAI,CAAC,QAAQ;AACX,iBAAW,UAAU;AACrB,UAAI,mBAAmB;AACrB,0BAAkB;AAAA,MACpB,OAAO;AACL,sBAAc,QAAQ;AAAA,MACxB;AAAA,IACF;AAAA,EACF,GAAG,cAAc,CAAC,GAAG,OAAO,WAAW,GAAG,KAAK,CAAC;AAChD,SAAO;AAAA,IACL,UAAU,WAAY;AACpB,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,UACL,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,iBAAiB,SAAS,SAAU,IAAI;AACtC,MAAI,KAAK,GAAG,OACV,QAAQ,OAAO,SAAS,OAAO,IAC/B,SAAS,GAAG;AACd,SAAO;AAAA,IACL,SAAS,CAAC,UAAU;AAAA,EACtB;AACF;AACA,IAAO,2BAAQ;;;AEnDf,IAAAC,gBAAuB;;;ACDvB,IAAAC,gBAAuB;;;ACAvB,SAAS,YAAY,SAAS,MAAM;AAClC,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,QAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,sBAAQ;;;ADTf,IAAI,cAAc,SAAU,SAAS,MAAM;AACzC,MAAI,cAAU,sBAAO;AAAA,IACnB;AAAA,IACA,KAAK;AAAA,IACL,aAAa;AAAA,EACf,CAAC,EAAE;AACH,MAAI,QAAQ,gBAAgB,SAAS,CAAC,oBAAY,QAAQ,MAAM,IAAI,GAAG;AACrE,YAAQ,OAAO;AACf,YAAQ,MAAM,QAAQ;AACtB,YAAQ,cAAc;AAAA,EACxB;AACA,SAAO,QAAQ;AACjB;AACA,IAAO,sBAAQ;;;AEff,IAAAC,gBAA0B;;;ACA1B,IAAAC,gBAAuB;AACvB,SAAS,UAAU,OAAO;AACxB,MAAI,UAAM,sBAAO,KAAK;AACtB,MAAI,UAAU;AACd,SAAO;AACT;AACA,IAAO,oBAAQ;;;ADFf,IAAI,aAAa,SAAU,IAAI;AAC7B,MAAI,eAAO;AACT,QAAI,CAAC,WAAW,EAAE,GAAG;AACnB,cAAQ,MAAM,oDAAoD,OAAO,OAAO,EAAE,CAAC;AAAA,IACrF;AAAA,EACF;AACA,MAAI,QAAQ,kBAAU,EAAE;AACxB,+BAAU,WAAY;AACpB,WAAO,WAAY;AACjB,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AACA,IAAO,qBAAQ;;;AEhBf,IAAI,QAAQ,oBAAI,IAAI;AACpB,IAAI,WAAW,SAAU,KAAK,WAAW,YAAY;AACnD,MAAI,eAAe,MAAM,IAAI,GAAG;AAChC,MAAI,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,OAAO;AAClF,iBAAa,aAAa,KAAK;AAAA,EACjC;AACA,MAAI,QAAQ;AACZ,MAAI,YAAY,IAAI;AAElB,YAAQ,WAAW,WAAY;AAC7B,YAAM,OAAO,GAAG;AAAA,IAClB,GAAG,SAAS;AAAA,EACd;AACA,QAAM,IAAI,KAAK,SAAS,SAAS,CAAC,GAAG,UAAU,GAAG;AAAA,IAChD;AAAA,EACF,CAAC,CAAC;AACJ;AACA,IAAI,WAAW,SAAU,KAAK;AAC5B,SAAO,MAAM,IAAI,GAAG;AACtB;AACA,IAAI,aAAa,SAAU,KAAK;AAC9B,MAAI,KAAK;AACP,QAAI,YAAY,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG;AAC/C,cAAU,QAAQ,SAAU,UAAU;AACpC,aAAO,MAAM,OAAO,QAAQ;AAAA,IAC9B,CAAC;AAAA,EACH,OAAO;AACL,UAAM,MAAM;AAAA,EACd;AACF;;;AC9BA,IAAI,eAAe,oBAAI,IAAI;AAC3B,IAAI,kBAAkB,SAAU,UAAU;AACxC,SAAO,aAAa,IAAI,QAAQ;AAClC;AACA,IAAI,kBAAkB,SAAU,UAAU,SAAS;AAGjD,eAAa,IAAI,UAAU,OAAO;AAElC,UAAQ,KAAK,SAAU,KAAK;AAC1B,iBAAa,OAAO,QAAQ;AAC5B,WAAO;AAAA,EACT,CAAC,EAAE,MAAM,WAAY;AACnB,iBAAa,OAAO,QAAQ;AAAA,EAC9B,CAAC;AACH;;;ACfA,IAAI,YAAY,CAAC;AACjB,IAAI,UAAU,SAAU,KAAK,MAAM;AACjC,MAAI,UAAU,GAAG,GAAG;AAClB,cAAU,GAAG,EAAE,QAAQ,SAAU,MAAM;AACrC,aAAO,KAAK,IAAI;AAAA,IAClB,CAAC;AAAA,EACH;AACF;AACA,IAAI,YAAY,SAAU,KAAK,UAAU;AACvC,MAAI,CAAC,UAAU,GAAG,GAAG;AACnB,cAAU,GAAG,IAAI,CAAC;AAAA,EACpB;AACA,YAAU,GAAG,EAAE,KAAK,QAAQ;AAC5B,SAAO,SAAS,cAAc;AAC5B,QAAI,QAAQ,UAAU,GAAG,EAAE,QAAQ,QAAQ;AAC3C,cAAU,GAAG,EAAE,OAAO,OAAO,CAAC;AAAA,EAChC;AACF;;;APVA,IAAI,iBAAiB,SAAU,eAAe,IAAI;AAChD,MAAI,WAAW,GAAG,UAChB,KAAK,GAAG,WACR,YAAY,OAAO,SAAS,IAAI,KAAK,MAAO,IAC5C,KAAK,GAAG,WACR,YAAY,OAAO,SAAS,IAAI,IAChC,iBAAiB,GAAG,UACpB,iBAAiB,GAAG;AACtB,MAAI,qBAAiB,sBAAO,MAAS;AACrC,MAAI,wBAAoB,sBAAO,MAAS;AACxC,MAAI,YAAY,SAAU,KAAK,YAAY;AACzC,QAAI,gBAAgB;AAClB,qBAAe,UAAU;AAAA,IAC3B,OAAO;AACL,eAAS,KAAK,WAAW,UAAU;AAAA,IACrC;AACA,YAAQ,KAAK,WAAW,IAAI;AAAA,EAC9B;AACA,MAAI,YAAY,SAAU,KAAK,QAAQ;AACrC,QAAI,WAAW,QAAQ;AACrB,eAAS,CAAC;AAAA,IACZ;AACA,QAAI,gBAAgB;AAClB,aAAO,eAAe,MAAM;AAAA,IAC9B;AACA,WAAO,SAAS,GAAG;AAAA,EACrB;AACA,sBAAY,WAAY;AACtB,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AAEA,QAAI,YAAY,UAAU,QAAQ;AAClC,QAAI,aAAa,OAAO,eAAe,KAAK,WAAW,MAAM,GAAG;AAC9D,oBAAc,MAAM,OAAO,UAAU;AACrC,oBAAc,MAAM,SAAS,UAAU;AACvC,UAAI,cAAc,MAAM,KAAK,IAAI,IAAI,UAAU,QAAQ,WAAW;AAChE,sBAAc,MAAM,UAAU;AAAA,MAChC;AAAA,IACF;AAEA,mBAAe,UAAU,UAAU,UAAU,SAAU,MAAM;AAC3D,oBAAc,SAAS;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,qBAAW,WAAY;AACrB,QAAIC;AACJ,KAACA,MAAK,eAAe,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc;AAAA,EAC3F,CAAC;AACD,MAAI,CAAC,UAAU;AACb,WAAO,CAAC;AAAA,EACV;AACA,SAAO;AAAA,IACL,UAAU,SAAU,QAAQ;AAC1B,UAAI,YAAY,UAAU,UAAU,MAAM;AAC1C,UAAI,CAAC,aAAa,CAAC,OAAO,eAAe,KAAK,WAAW,MAAM,GAAG;AAChE,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,cAAc,MAAM,KAAK,IAAI,IAAI,UAAU,QAAQ,WAAW;AAChE,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU;AAAA,UACtE,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,MACF,OAAO;AAEL,eAAO;AAAA,UACL,MAAM,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU;AAAA,UACtE,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW,SAAU,SAAS,MAAM;AAClC,UAAI,iBAAiB,gBAAgB,QAAQ;AAE7C,UAAI,kBAAkB,mBAAmB,kBAAkB,SAAS;AAClE,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,uBAAiB,QAAQ,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAC7E,wBAAkB,UAAU;AAC5B,sBAAgB,UAAU,cAAc;AACxC,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW,SAAU,MAAM,QAAQ;AACjC,UAAIA;AACJ,UAAI,UAAU;AAEZ,SAACA,MAAK,eAAe,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc;AACzF,kBAAU,UAAU;AAAA,UAClB;AAAA,UACA;AAAA,UACA,MAAM,KAAK,IAAI;AAAA,QACjB,CAAC;AAED,uBAAe,UAAU,UAAU,UAAU,SAAU,GAAG;AACxD,wBAAc,SAAS;AAAA,YACrB,MAAM;AAAA,UACR,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,UAAU,SAAU,MAAM;AACxB,UAAIA;AACJ,UAAI,UAAU;AAEZ,SAACA,MAAK,eAAe,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc;AACzF,kBAAU,UAAU;AAAA,UAClB;AAAA,UACA,QAAQ,cAAc,MAAM;AAAA,UAC5B,MAAM,KAAK,IAAI;AAAA,QACjB,CAAC;AAED,uBAAe,UAAU,UAAU,UAAU,SAAU,GAAG;AACxD,wBAAc,SAAS;AAAA,YACrB,MAAM;AAAA,UACR,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,yBAAQ;;;AQvIf,sBAAqB;AACrB,IAAAC,gBAA2C;AAC3C,IAAI,oBAAoB,SAAU,eAAe,IAAI;AACnD,MAAI,eAAe,GAAG,cACpB,kBAAkB,GAAG,iBACrB,mBAAmB,GAAG,kBACtB,kBAAkB,GAAG;AACvB,MAAI,mBAAe,sBAAO,MAAS;AACnC,MAAI,cAAU,uBAAQ,WAAY;AAChC,QAAI,MAAM,CAAC;AACX,QAAI,oBAAoB,QAAW;AACjC,UAAI,UAAU;AAAA,IAChB;AACA,QAAI,qBAAqB,QAAW;AAClC,UAAI,WAAW;AAAA,IACjB;AACA,QAAI,oBAAoB,QAAW;AACjC,UAAI,UAAU;AAAA,IAChB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,iBAAiB,kBAAkB,eAAe,CAAC;AACvD,+BAAU,WAAY;AACpB,QAAI,cAAc;AAChB,UAAI,oBAAoB,cAAc,SAAS,KAAK,aAAa;AACjE,mBAAa,cAAU,gBAAAC,SAAS,SAAU,UAAU;AAClD,iBAAS;AAAA,MACX,GAAG,cAAc,OAAO;AAGxB,oBAAc,WAAW,WAAY;AACnC,YAAI,OAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,eAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,cAAIC;AACJ,WAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc,WAAY;AACjG,8BAAkB,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,UACpG,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO,WAAY;AACjB,YAAIA;AACJ,SAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO;AAC3E,sBAAc,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,OAAO,CAAC;AAC1B,MAAI,CAAC,cAAc;AACjB,WAAO,CAAC;AAAA,EACV;AACA,SAAO;AAAA,IACL,UAAU,WAAY;AACpB,UAAIA;AACJ,OAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO;AAAA,IAC7E;AAAA,EACF;AACF;AACA,IAAO,4BAAQ;;;AC3Df,IAAAC,iBAAuB;AACvB,IAAI,wBAAwB,SAAU,eAAe,IAAI;AACvD,MAAI,eAAe,GAAG,cACpB,QAAQ,GAAG;AACb,MAAI,eAAW,uBAAO,MAAS;AAC/B,MAAI,CAAC,cAAc;AACjB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,gBAAgB,WAAY;AAC9B,QAAI,SAAS,SAAS;AACpB,mBAAa,SAAS,OAAO;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AAAA,IACL,UAAU,WAAY;AACpB,oBAAc;AAId,UAAI,UAAU,OAAO;AACnB,iBAAS,UAAU,WAAW,WAAY;AACxC,wBAAc,SAAS;AAAA,YACrB,SAAS;AAAA,UACX,CAAC;AAAA,QACH,GAAG,YAAY;AAAA,MACjB;AACA,aAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IACA,WAAW,WAAY;AACrB,oBAAc;AAAA,IAChB;AAAA,IACA,UAAU,WAAY;AACpB,oBAAc;AAAA,IAChB;AAAA,EACF;AACF;AACA,IAAO,gCAAQ;;;ACtCf,IAAAC,iBAAuB;;;ACAvB,IAAI,YAAY,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AACvF,IAAO,oBAAQ;;;ACAA,SAAR,oBAAqC;AAC1C,MAAI,mBAAW;AACb,WAAO,SAAS,oBAAoB;AAAA,EACtC;AACA,SAAO;AACT;;;ACJA,IAAIC,aAAY,CAAC;AACjB,SAASC,WAAU,UAAU;AAC3B,EAAAD,WAAU,KAAK,QAAQ;AACvB,SAAO,SAAS,cAAc;AAC5B,QAAI,QAAQA,WAAU,QAAQ,QAAQ;AACtC,IAAAA,WAAU,OAAO,OAAO,CAAC;AAAA,EAC3B;AACF;AACA,IAAI,mBAAW;AACT,eAAa,WAAY;AAC3B,QAAI,CAAC,kBAAkB;AAAG;AAC1B,aAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACzC,UAAI,WAAWA,WAAU,CAAC;AAC1B,eAAS;AAAA,IACX;AAAA,EACF;AACA,SAAO,iBAAiB,oBAAoB,YAAY,KAAK;AAC/D;AARM;AASN,IAAO,6BAAQC;;;AHhBf,IAAI,mBAAmB,SAAU,eAAe,IAAI;AAClD,MAAI,kBAAkB,GAAG,iBACvB,KAAK,GAAG,mBACR,oBAAoB,OAAO,SAAS,OAAO,IAC3C,KAAK,GAAG,wBACR,yBAAyB,OAAO,SAAS,KAAK;AAChD,MAAI,eAAW,uBAAO,MAAS;AAC/B,MAAI,qBAAiB,uBAAO,MAAS;AACrC,MAAI,eAAW,uBAAO,CAAC;AACvB,MAAI,cAAc,WAAY;AAC5B,QAAIC;AACJ,QAAI,SAAS,SAAS;AACpB,mBAAa,SAAS,OAAO;AAAA,IAC/B;AACA,KAACA,MAAK,eAAe,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc;AAAA,EAC3F;AACA,0BAAgB,WAAY;AAC1B,QAAI,CAAC,iBAAiB;AACpB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AACpB,MAAI,CAAC,iBAAiB;AACpB,WAAO,CAAC;AAAA,EACV;AACA,SAAO;AAAA,IACL,UAAU,WAAY;AACpB,kBAAY;AAAA,IACd;AAAA,IACA,SAAS,WAAY;AACnB,eAAS,WAAW;AAAA,IACtB;AAAA,IACA,WAAW,WAAY;AACrB,eAAS,UAAU;AAAA,IACrB;AAAA,IACA,WAAW,WAAY;AACrB,UAAI,2BAA2B;AAAA,MAE/B,2BAA2B,MAAM,SAAS,WAAW,wBAAwB;AAC3E,iBAAS,UAAU,WAAW,WAAY;AAExC,cAAI,CAAC,qBAAqB,CAAC,kBAAkB,GAAG;AAC9C,2BAAe,UAAU,2BAAmB,WAAY;AACtD,4BAAc,QAAQ;AAAA,YACxB,CAAC;AAAA,UACH,OAAO;AACL,0BAAc,QAAQ;AAAA,UACxB;AAAA,QACF,GAAG,eAAe;AAAA,MACpB,OAAO;AACL,iBAAS,UAAU;AAAA,MACrB;AAAA,IACF;AAAA,IACA,UAAU,WAAY;AACpB,kBAAY;AAAA,IACd;AAAA,EACF;AACF;AACA,IAAO,2BAAQ;;;AI7Df,IAAAC,iBAAkC;;;ACCnB,SAAR,MAAuB,IAAI,UAAU;AAC1C,MAAI,UAAU;AACd,SAAO,WAAY;AACjB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,QAAI;AAAS;AACb,cAAU;AACV,OAAG,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AACvD,eAAW,WAAY;AACrB,gBAAU;AAAA,IACZ,GAAG,QAAQ;AAAA,EACb;AACF;;;ACdA,IAAI,WAAW,WAAY;AACzB,MAAI,qBAAa,OAAO,UAAU,WAAW,aAAa;AACxD,WAAO,UAAU;AAAA,EACnB;AACA,SAAO;AACT;AACA,IAAO,mBAAQ;;;ACHf,IAAIC,aAAY,CAAC;AACjB,SAASC,WAAU,UAAU;AAC3B,EAAAD,WAAU,KAAK,QAAQ;AACvB,SAAO,SAAS,cAAc;AAC5B,QAAI,QAAQA,WAAU,QAAQ,QAAQ;AACtC,QAAI,QAAQ,IAAI;AACd,MAAAA,WAAU,OAAO,OAAO,CAAC;AAAA,IAC3B;AAAA,EACF;AACF;AACA,IAAI,mBAAW;AACT,eAAa,WAAY;AAC3B,QAAI,CAAC,kBAAkB,KAAK,CAAC,iBAAS;AAAG;AACzC,aAAS,IAAI,GAAG,IAAIA,WAAU,QAAQ,KAAK;AACzC,UAAI,WAAWA,WAAU,CAAC;AAC1B,eAAS;AAAA,IACX;AAAA,EACF;AACA,SAAO,iBAAiB,oBAAoB,YAAY,KAAK;AAC7D,SAAO,iBAAiB,SAAS,YAAY,KAAK;AACpD;AATM;AAUN,IAAO,yBAAQC;;;AHrBf,IAAI,gCAAgC,SAAU,eAAe,IAAI;AAC/D,MAAI,uBAAuB,GAAG,sBAC5B,KAAK,GAAG,eACR,gBAAgB,OAAO,SAAS,MAAO;AACzC,MAAI,qBAAiB,uBAAO,MAAS;AACrC,MAAI,gBAAgB,WAAY;AAC9B,QAAIC;AACJ,KAACA,MAAK,eAAe,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc;AAAA,EAC3F;AACA,gCAAU,WAAY;AACpB,QAAI,sBAAsB;AACxB,UAAI,iBAAiB,MAAM,cAAc,QAAQ,KAAK,aAAa,GAAG,aAAa;AACnF,qBAAe,UAAU,uBAAe,WAAY;AAClD,uBAAe;AAAA,MACjB,CAAC;AAAA,IACH;AACA,WAAO,WAAY;AACjB,oBAAc;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,sBAAsB,aAAa,CAAC;AACxC,qBAAW,WAAY;AACrB,kBAAc;AAAA,EAChB,CAAC;AACD,SAAO,CAAC;AACV;AACA,IAAO,wCAAQ;;;AI7Bf,IAAAC,iBAAuB;AACvB,IAAI,iBAAiB,SAAU,eAAe,IAAI;AAChD,MAAI,gBAAgB,GAAG,eACrB,aAAa,GAAG;AAClB,MAAI,eAAW,uBAAO,MAAS;AAC/B,MAAI,eAAW,uBAAO,CAAC;AACvB,MAAI,qBAAiB,uBAAO,KAAK;AACjC,MAAI,CAAC,YAAY;AACf,WAAO,CAAC;AAAA,EACV;AACA,SAAO;AAAA,IACL,UAAU,WAAY;AACpB,UAAI,CAAC,eAAe,SAAS;AAC3B,iBAAS,UAAU;AAAA,MACrB;AACA,qBAAe,UAAU;AACzB,UAAI,SAAS,SAAS;AACpB,qBAAa,SAAS,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,WAAW,WAAY;AACrB,eAAS,UAAU;AAAA,IACrB;AAAA,IACA,SAAS,WAAY;AACnB,eAAS,WAAW;AACpB,UAAI,eAAe,MAAM,SAAS,WAAW,YAAY;AAEvD,YAAI,UAAU,kBAAkB,QAAQ,kBAAkB,SAAS,gBAAgB,KAAK,IAAI,MAAO,KAAK,IAAI,GAAG,SAAS,OAAO,GAAG,GAAK;AACvI,iBAAS,UAAU,WAAW,WAAY;AACxC,yBAAe,UAAU;AACzB,wBAAc,QAAQ;AAAA,QACxB,GAAG,OAAO;AAAA,MACZ,OAAO;AACL,iBAAS,UAAU;AAAA,MACrB;AAAA,IACF;AAAA,IACA,UAAU,WAAY;AACpB,eAAS,UAAU;AACnB,UAAI,SAAS,SAAS;AACpB,qBAAa,SAAS,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,yBAAQ;;;AC3Cf,sBAAqB;AACrB,IAAAC,iBAAkC;AAClC,IAAI,oBAAoB,SAAU,eAAe,IAAI;AACnD,MAAI,eAAe,GAAG,cACpB,kBAAkB,GAAG,iBACrB,mBAAmB,GAAG;AACxB,MAAI,mBAAe,uBAAO,MAAS;AACnC,MAAI,UAAU,CAAC;AACf,MAAI,oBAAoB,QAAW;AACjC,YAAQ,UAAU;AAAA,EACpB;AACA,MAAI,qBAAqB,QAAW;AAClC,YAAQ,WAAW;AAAA,EACrB;AACA,gCAAU,WAAY;AACpB,QAAI,cAAc;AAChB,UAAI,oBAAoB,cAAc,SAAS,KAAK,aAAa;AACjE,mBAAa,cAAU,gBAAAC,SAAS,SAAU,UAAU;AAClD,iBAAS;AAAA,MACX,GAAG,cAAc,OAAO;AAGxB,oBAAc,WAAW,WAAY;AACnC,YAAI,OAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,eAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,cAAIC;AACJ,WAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc,WAAY;AACjG,8BAAkB,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,UACpG,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO,WAAY;AACjB,YAAIA;AACJ,sBAAc,WAAW;AACzB,SAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO;AAAA,MAC7E;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,iBAAiB,gBAAgB,CAAC;AACpD,MAAI,CAAC,cAAc;AACjB,WAAO,CAAC;AAAA,EACV;AACA,SAAO;AAAA,IACL,UAAU,WAAY;AACpB,UAAIA;AACJ,OAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO;AAAA,IAC7E;AAAA,EACF;AACF;AACA,IAAO,4BAAQ;;;ACpDf,IAAAC,iBAA0B;AAG1B,IAAI,WAAW,SAAU,IAAI;AAC3B,MAAI,eAAO;AACT,QAAI,CAAC,WAAW,EAAE,GAAG;AACnB,cAAQ,MAAM,gEAAiE,OAAO,OAAO,IAAI,IAAK,CAAC;AAAA,IACzG;AAAA,EACF;AACA,gCAAU,WAAY;AACpB,WAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,EAC7C,GAAG,CAAC,CAAC;AACP;AACA,IAAO,mBAAQ;;;ACZf,IAAAC,iBAAsC;AACtC,IAAI,YAAY,WAAY;AAC1B,MAAI,KAAK,WAAO,yBAAS,CAAC,CAAC,GAAG,CAAC,GAC7B,WAAW,GAAG,CAAC;AACjB,aAAO,4BAAY,WAAY;AAC7B,WAAO,SAAS,CAAC,CAAC;AAAA,EACpB,GAAG,CAAC,CAAC;AACP;AACA,IAAO,oBAAQ;;;ACPf,IAAI;AAAA;AAAA,EAAqB,WAAY;AACnC,aAASC,OAAM,YAAY,SAASC,YAAWC,YAAW;AACxD,UAAIA,eAAc,QAAQ;AACxB,QAAAA,aAAY,CAAC;AAAA,MACf;AACA,WAAK,aAAa;AAClB,WAAK,UAAU;AACf,WAAK,YAAYD;AACjB,WAAK,YAAYC;AACjB,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,QACX,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AACA,WAAK,QAAQ,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG;AAAA,QACvD,SAAS,CAAC,QAAQ;AAAA,MACpB,CAAC,GAAGA,UAAS;AAAA,IACf;AACA,IAAAF,OAAM,UAAU,WAAW,SAAU,GAAG;AACtC,UAAI,MAAM,QAAQ;AAChB,YAAI,CAAC;AAAA,MACP;AACA,WAAK,QAAQ,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC;AACjD,WAAK,UAAU;AAAA,IACjB;AACA,IAAAA,OAAM,UAAU,mBAAmB,SAAU,OAAO;AAClD,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,MAC7B;AAEA,UAAI,IAAI,KAAK,YAAY,IAAI,SAAU,GAAG;AACxC,YAAI;AACJ,gBAAQ,KAAK,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,MAAM,IAAI,cAAc,CAAC,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAAA,MACvH,CAAC,EAAE,OAAO,OAAO;AACjB,aAAO,OAAO,OAAO,MAAM,QAAQ,cAAc,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC;AAAA,IAC1E;AACA,IAAAA,OAAM,UAAU,WAAW,WAAY;AACrC,UAAI,SAAS,CAAC;AACd,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,eAAO,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AACjD,YAAI,cAAc,IAAI,IAAI,SAAS,IAAI,WAAW,OAAO,gBAAgB,KAAK;AAC9E,YAAI;AACJ,YAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxC,eAAO,YAAY,MAAM,SAAU,IAAI;AACrC,kBAAQ,GAAG,OAAO;AAAA,YAChB,KAAK;AACH,mBAAK,SAAS;AACd,6BAAe,KAAK;AACpB,mBAAK,KAAK,iBAAiB,YAAY,MAAM,GAAG,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,QAAQ,IAAI,KAAK,GAAG,WAAW,YAAY,OAAO,SAAS,QAAQ,IAAI,QAAQ,OAAO,IAAI,CAAC,WAAW,WAAW,CAAC;AAE7M,kBAAI,SAAS;AACX,uBAAO,CAAC,GAAc,IAAI,QAAQ,WAAY;AAAA,gBAAC,CAAC,CAAC;AAAA,cACnD;AACA,mBAAK,SAAS,SAAS;AAAA,gBACrB,SAAS;AAAA,gBACT;AAAA,cACF,GAAG,KAAK,CAAC;AAET,kBAAI,WAAW;AACb,uBAAO,CAAC,GAAc,QAAQ,QAAQ,MAAM,IAAI,CAAC;AAAA,cACnD;AACA,eAAC,MAAM,KAAK,KAAK,SAAS,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,MAAM;AAC3F,iBAAG,QAAQ;AAAA,YACb,KAAK;AACH,iBAAG,KAAK,KAAK,CAAC,GAAG,GAAE,EAAE,CAAC,CAAC;AACvB,+BAAiB,KAAK,iBAAiB,aAAa,KAAK,WAAW,SAAS,MAAM,EAAE;AACrF,kBAAI,CAAC,gBAAgB;AACnB,kCAAkB,KAAK,KAAK,YAAY,QAAQ,MAAM,IAAI,cAAc,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,CAAC;AAAA,cACpG;AACA,qBAAO,CAAC,GAAa,cAAc;AAAA,YACrC,KAAK;AACH,oBAAM,GAAG,KAAK;AACd,kBAAI,iBAAiB,KAAK,OAAO;AAE/B,uBAAO,CAAC,GAAc,IAAI,QAAQ,WAAY;AAAA,gBAAC,CAAC,CAAC;AAAA,cACnD;AAEA,mBAAK,SAAS;AAAA,gBACZ,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,SAAS;AAAA,cACX,CAAC;AACD,eAAC,MAAM,KAAK,KAAK,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK,MAAM;AACjG,mBAAK,iBAAiB,aAAa,KAAK,MAAM;AAC9C,eAAC,MAAM,KAAK,KAAK,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,QAAQ,KAAK,MAAS;AAC5G,kBAAI,iBAAiB,KAAK,OAAO;AAC/B,qBAAK,iBAAiB,aAAa,QAAQ,KAAK,MAAS;AAAA,cAC3D;AACA,qBAAO,CAAC,GAAc,GAAG;AAAA,YAC3B,KAAK;AACH,wBAAU,GAAG,KAAK;AAClB,kBAAI,iBAAiB,KAAK,OAAO;AAE/B,uBAAO,CAAC,GAAc,IAAI,QAAQ,WAAY;AAAA,gBAAC,CAAC,CAAC;AAAA,cACnD;AACA,mBAAK,SAAS;AAAA,gBACZ,OAAO;AAAA,gBACP,SAAS;AAAA,cACX,CAAC;AACD,eAAC,MAAM,KAAK,KAAK,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,SAAS,MAAM;AACnG,mBAAK,iBAAiB,WAAW,SAAS,MAAM;AAChD,eAAC,MAAM,KAAK,KAAK,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,QAAQ,QAAW,OAAO;AAChH,kBAAI,iBAAiB,KAAK,OAAO;AAC/B,qBAAK,iBAAiB,aAAa,QAAQ,QAAW,OAAO;AAAA,cAC/D;AACA,oBAAM;AAAA,YACR,KAAK;AACH,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UACxB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,IAAAA,OAAM,UAAU,MAAM,WAAY;AAChC,UAAI,QAAQ;AACZ,UAAI,SAAS,CAAC;AACd,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,eAAO,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,WAAK,SAAS,MAAM,MAAM,cAAc,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,CAAC,EAAE,MAAM,SAAU,OAAO;AACzF,YAAI,CAAC,MAAM,QAAQ,SAAS;AAC1B,kBAAQ,MAAM,KAAK;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAAA,OAAM,UAAU,SAAS,WAAY;AACnC,WAAK,SAAS;AACd,WAAK,SAAS;AAAA,QACZ,SAAS;AAAA,MACX,CAAC;AACD,WAAK,iBAAiB,UAAU;AAAA,IAClC;AACA,IAAAA,OAAM,UAAU,UAAU,WAAY;AAEpC,WAAK,IAAI,MAAM,MAAM,cAAc,CAAC,GAAG,OAAO,KAAK,MAAM,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,IAChF;AACA,IAAAA,OAAM,UAAU,eAAe,WAAY;AAEzC,aAAO,KAAK,SAAS,MAAM,MAAM,cAAc,CAAC,GAAG,OAAO,KAAK,MAAM,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,IAC5F;AACA,IAAAA,OAAM,UAAU,SAAS,SAAU,MAAM;AACvC,UAAI,aAAa,WAAW,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,IAAI;AAC5D,WAAK,iBAAiB,YAAY,UAAU;AAC5C,WAAK,SAAS;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAO,gBAAQ;;;AClJf,SAAS,oBAAoB,SAAS,SAAS,SAAS;AACtD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,KAAK,QAAQ,QACf,SAAS,OAAO,SAAS,QAAQ,IACjC,KAAK,QAAQ,OACb,QAAQ,OAAO,SAAS,OAAO,IAC/B,OAAO,OAAO,SAAS,CAAC,UAAU,OAAO,CAAC;AAC5C,MAAI,eAAO;AACT,QAAI,QAAQ,iBAAiB,CAAC,MAAM,QAAQ,QAAQ,aAAa,GAAG;AAClE,cAAQ,KAAK,wCAAwC,OAAO,OAAO,QAAQ,aAAa,CAAC;AAAA,IAC3F;AAAA,EACF;AACA,MAAI,eAAe,SAAS;AAAA,IAC1B;AAAA,IACA;AAAA,EACF,GAAG,IAAI;AACP,MAAI,aAAa,kBAAU,OAAO;AAClC,MAAI,SAAS,kBAAU;AACvB,MAAI,gBAAgB,oBAAY,WAAY;AAC1C,QAAIG,aAAY,QAAQ,IAAI,SAAU,GAAG;AACvC,UAAIC;AACJ,cAAQA,MAAK,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,GAAG,YAAY;AAAA,IAC3H,CAAC,EAAE,OAAO,OAAO;AACjB,WAAO,IAAI,cAAM,YAAY,cAAc,QAAQ,OAAO,OAAO,MAAM,QAAQ,cAAc,CAAC,CAAC,CAAC,GAAG,OAAOD,UAAS,GAAG,KAAK,CAAC,CAAC;AAAA,EAC/H,GAAG,CAAC,CAAC;AACL,gBAAc,UAAU;AAExB,gBAAc,cAAc,QAAQ,IAAI,SAAU,GAAG;AACnD,WAAO,EAAE,eAAe,YAAY;AAAA,EACtC,CAAC;AACD,mBAAS,WAAY;AACnB,QAAI,CAAC,UAAU,OAAO;AAEpB,UAAI,SAAS,cAAc,MAAM,UAAU,QAAQ,iBAAiB,CAAC;AAErE,oBAAc,IAAI,MAAM,eAAe,cAAc,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,CAAC;AAAA,IACjF;AAAA,EACF,CAAC;AACD,qBAAW,WAAY;AACrB,kBAAc,OAAO;AAAA,EACvB,CAAC;AACD,SAAO;AAAA,IACL,SAAS,cAAc,MAAM;AAAA,IAC7B,MAAM,cAAc,MAAM;AAAA,IAC1B,OAAO,cAAc,MAAM;AAAA,IAC3B,QAAQ,cAAc,MAAM,UAAU,CAAC;AAAA,IACvC,QAAQ,sBAAc,cAAc,OAAO,KAAK,aAAa,CAAC;AAAA,IAC9D,SAAS,sBAAc,cAAc,QAAQ,KAAK,aAAa,CAAC;AAAA,IAChE,cAAc,sBAAc,cAAc,aAAa,KAAK,aAAa,CAAC;AAAA,IAC1E,KAAK,sBAAc,cAAc,IAAI,KAAK,aAAa,CAAC;AAAA,IACxD,UAAU,sBAAc,cAAc,SAAS,KAAK,aAAa,CAAC;AAAA,IAClE,QAAQ,sBAAc,cAAc,OAAO,KAAK,aAAa,CAAC;AAAA,EAChE;AACF;AACA,IAAO,8BAAQ;;;AChDf,SAAS,WAAW,SAAS,SAAS,SAAS;AAC7C,SAAO,4BAAoB,SAAS,SAAS,cAAc,cAAc,CAAC,GAAG,OAAO,WAAW,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,2BAAmB,+BAAuB,0BAAkB,uCAA+B,2BAAmB,0BAAkB,wBAAgB,sBAAc,GAAG,KAAK,CAAC;AACtR;AACA,IAAO,qBAAQ;;;ACpBf,IAAOE,sBAAQ;;;A5BCf,IAAI,gBAAgB,SAAU,SAAS,SAAS;AAC9C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,KAAK,QAAQ,iBACf,kBAAkB,OAAO,SAAS,KAAK,IACvC,KAAK,QAAQ,gBACb,iBAAiB,OAAO,SAAS,IAAI,IACrC,OAAO,OAAO,SAAS,CAAC,mBAAmB,gBAAgB,CAAC;AAC9D,MAAI,SAASC,oBAAW,SAAS,SAAS;AAAA,IACxC,eAAe,CAAC;AAAA,MACd,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,IACD,mBAAmB,WAAY;AAE7B,oBAAc,CAAC;AAAA,IACjB;AAAA,EACF,GAAG,IAAI,CAAC;AACR,MAAI,KAAK,OAAO,OAAO,CAAC,KAAK,CAAC,GAC5B,KAAK,GAAG,SACR,UAAU,OAAO,SAAS,IAAI,IAC9B,KAAK,GAAG,UACR,WAAW,OAAO,SAAS,kBAAkB;AAC/C,MAAI,UAAU,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU;AAClF,MAAI,gBAAY,wBAAQ,WAAY;AAClC,WAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,EACnC,GAAG,CAAC,UAAU,KAAK,CAAC;AACpB,MAAI,WAAW,SAAU,GAAG,GAAG;AAC7B,QAAI,YAAY,KAAK,IAAI,IAAI;AAC7B,QAAI,aAAa,KAAK,IAAI,IAAI;AAC9B,QAAI,gBAAgB,KAAK,KAAK,QAAQ,UAAU;AAChD,QAAI,YAAY,eAAe;AAC7B,kBAAY,KAAK,IAAI,GAAG,aAAa;AAAA,IACvC;AACA,QAAIC,MAAK,OAAO,OAAO,UAAU,CAAC,CAAC,GACjCC,MAAKD,IAAG,CAAC,GACT,sBAAsBC,QAAO,SAAS,CAAC,IAAIA,KAC3C,aAAaD,IAAG,MAAM,CAAC;AACzB,WAAO,IAAI,MAAM,QAAQ,cAAc,CAAC,SAAS,SAAS,CAAC,GAAG,mBAAmB,GAAG;AAAA,MAClF,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,CAAC,GAAG,OAAO,UAAU,GAAG,KAAK,CAAC;AAAA,EACjC;AACA,MAAI,gBAAgB,SAAU,GAAG;AAC/B,aAAS,GAAG,QAAQ;AAAA,EACtB;AACA,MAAI,iBAAiB,SAAU,GAAG;AAChC,aAAS,SAAS,CAAC;AAAA,EACrB;AACA,SAAO,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG;AAAA,IACpC,YAAY;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,sBAAc,QAAQ;AAAA,MAChC,eAAe,sBAAc,aAAa;AAAA,MAC1C,gBAAgB,sBAAc,cAAc;AAAA,IAC9C;AAAA,EACF,CAAC;AACH;AACA,IAAO,wBAAQ;;;AJ9Df,IAAI,eAAe,SAAU,SAAS,SAAS;AAC7C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,OAAO,QAAQ,MACjB,KAAK,QAAQ,aACb,cAAc,OAAO,SAAS,WAAW,IACzC,gBAAgB,QAAQ,eACxB,KAAK,QAAQ,QACb,SAAS,OAAO,SAAS,QAAQ,IACjC,KAAK,QAAQ,aACb,cAAc,OAAO,SAAS,CAAC,IAAI,IACnC,KAAK,QAAQ,OACb,QAAQ,OAAO,SAAS,OAAO,IAC/B,OAAO,OAAO,SAAS,CAAC,QAAQ,eAAe,iBAAiB,UAAU,eAAe,OAAO,CAAC;AACnG,MAAI,SAAS,sBAAc,SAAS,SAAS,SAAS;AAAA,IACpD;AAAA,IACA,QAAQ;AAAA,EACV,GAAG,IAAI,GAAG;AAAA,IACR,WAAW,WAAY;AACrB,UAAIE;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MACzB;AAEA,oBAAc,UAAU;AACxB,OAACA,MAAK,KAAK,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,MAAMA,KAAI,cAAc,CAAC,IAAI,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAAA,IACzH;AAAA,EACF,CAAC,CAAC;AACF,MAAI,KAAK,OAAO,QACd,SAAS,OAAO,SAAS,CAAC,IAAI,IAC9B,MAAM,OAAO;AACf,MAAI,qBAAqB,OAAO,CAAC,KAAK,CAAC;AACvC,MAAI,KAAK,WAAO,0BAAU,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,SAAS,WAAW,GAAG,CAAC,GAC3I,OAAO,GAAG,CAAC,GACX,UAAU,GAAG,CAAC;AAChB,MAAI,qBAAiB,uBAAO,CAAC,CAAC;AAC9B,MAAI,2BAAuB,uBAAO,CAAC,CAAC;AACpC,MAAI,oBAAgB,uBAAO,KAAK;AAChC,MAAI,WAAW,CAAC,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAEnE,MAAI,uBAAuB,WAAY;AACrC,QAAI,CAAC,MAAM;AACT,aAAO,CAAC;AAAA,IACV;AAEA,QAAI,UAAU;AACZ,aAAO,KAAK,eAAe,MAAM,WAAY;AAC3C,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,QAAI,iBAAiB,KAAK,eAAe;AACzC,QAAI,oBAAoB,CAAC;AACzB,WAAO,KAAK,cAAc,EAAE,QAAQ,SAAU,KAAK;AACjD,UAAI,KAAK,mBAAmB,KAAK,iBAAiB,GAAG,IAAI,MAAM;AAC7D,0BAAkB,GAAG,IAAI,eAAe,GAAG;AAAA,MAC7C;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,WAAY;AAC/B,QAAI,CAAC,MAAM;AACT,aAAO,QAAQ,QAAQ,CAAC,CAAC;AAAA,IAC3B;AACA,QAAI,oBAAoB,qBAAqB;AAC7C,QAAI,SAAS,OAAO,KAAK,iBAAiB;AAE1C,QAAI,UAAU;AACZ,aAAO,KAAK,eAAe,MAAM;AAAA,IACnC;AAEA,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,WAAK,eAAe,QAAQ,SAAU,QAAQ,QAAQ;AACpD,YAAI,QAAQ;AACV,iBAAO,MAAM;AAAA,QACf,OAAO;AACL,kBAAQ,MAAM;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,MAAI,cAAc,WAAY;AAC5B,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AAEA,QAAI,UAAU;AACZ,aAAO,KAAK,eAAe,eAAe,OAAO;AAAA,IACnD;AAEA,QAAI,oBAAoB,CAAC;AACzB,WAAO,KAAK,eAAe,OAAO,EAAE,QAAQ,SAAU,KAAK;AACzD,UAAI,KAAK,mBAAmB,KAAK,iBAAiB,GAAG,IAAI,MAAM;AAC7D,0BAAkB,GAAG,IAAI,eAAe,QAAQ,GAAG;AAAA,MACrD;AAAA,IACF,CAAC;AACD,SAAK,eAAe,iBAAiB;AAAA,EACvC;AACA,MAAI,aAAa,WAAY;AAC3B,QAAI,oBAAoB,qBAAqB;AAC7C,mBAAe,UAAU,SAAS,SAAS,CAAC,GAAG,eAAe,OAAO,GAAG,iBAAiB;AACzF,YAAQ,SAAU,GAAG;AACnB,aAAO,MAAM,WAAW,YAAY;AAAA,IACtC,CAAC;AAAA,EACH;AACA,MAAI,UAAU,SAAU,gBAAgB;AACtC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,eAAW,WAAY;AACrB,qBAAe,EAAE,KAAK,SAAU,QAAQ;AACtC,YAAI,WAAW,QAAQ;AACrB,mBAAS,CAAC;AAAA,QACZ;AACA,YAAI,aAAa,kBAAkB,SAAS,SAAS;AAAA,UACnD,UAAU,QAAQ,mBAAmB;AAAA,QACvC,IAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG;AAAA,UACrE,SAAS;AAAA,QACX,CAAC;AACD,YAAI,CAAC,MAAM;AAET,cAAI,UAAU;AACd;AAAA,QACF;AAEA,uBAAe,UAAU,SAAS,SAAS,CAAC,GAAG,eAAe,OAAO,GAAG,MAAM;AAE9E,YAAI,YAAY,QAAQ;AAAA,UACtB,aAAa,eAAe;AAAA,UAC5B;AAAA,QACF,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,SAAU,KAAK;AACtB,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,WAAY;AACtB,QAAIA,KAAIC;AACR,QAAI,MAAM;AACR,WAAK,YAAY;AAAA,IACnB;AACA,YAAQ,SAAS,SAAS,CAAC,IAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG;AAAA,MACrH,UAAU,QAAQ,qBAAqBA,OAAMD,MAAK,QAAQ,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,CAAC,OAAO,QAAQC,QAAO,SAAS,SAASA,IAAG,aAAa;AAAA,MAC1K,SAAS;AAAA,IACX,CAAC,CAAC;AAAA,EACJ;AACA,MAAI,SAAS,SAAU,GAAG;AACxB,QAAID,KAAIC,KAAIC;AACZ,KAACF,MAAK,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,oBAAoB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,CAAC;AAC5G,YAAQ,cAAc,UAAU,SAAY,SAAS;AAAA,MACnD,UAAU,QAAQ,qBAAqBE,OAAMD,MAAK,QAAQ,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,CAAC,OAAO,QAAQC,QAAO,SAAS,SAASA,IAAG,aAAa;AAAA,MAC1K,SAAS;AAAA,IACX,IAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAAA,EAC5F;AACA,MAAI,gBAAgB,SAAU,YAAY,SAAS,QAAQ,OAAO;AAChE,QAAIF,MAAK,OAAO,UAAU,CAAC,CAAC,GAC1B,sBAAsBA,IAAG,CAAC,GAC1B,aAAaA,IAAG,MAAM,CAAC;AACzB,QAAI,MAAM,QAAQ,cAAc,CAAC,SAAS,SAAS,CAAC,GAAG,mBAAmB,GAAG;AAAA,MAC3E,SAAS,WAAW;AAAA,MACpB,UAAU,WAAW;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC,GAAG,OAAO,UAAU,GAAG,KAAK,CAAC;AAAA,EACjC;AAEA,gCAAU,WAAY;AAEpB,QAAI,OAAO,SAAS,GAAG;AACrB,qBAAe,WAAW,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,gBAAgB,CAAC;AACtI,kBAAY;AAEZ,UAAI,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,CAAC;AAC1D;AAAA,IACF;AACA,QAAI,OAAO;AACT,qBAAe,WAAW,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,CAAC,MAAM,CAAC;AAC9G,kBAAY;AACZ,UAAI,CAAC,QAAQ;AACX,gBAAQ,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,CAAC,CAAC;AAAA,MACxF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,0BAAgB,WAAY;AAC1B,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,gBAAY;AAAA,EACd,GAAG,CAAC,IAAI,CAAC;AAET,MAAI,iBAAa,uBAAO,KAAK;AAC7B,aAAW,UAAU;AACrB,0BAAgB,WAAY;AAC1B,QAAI,CAAC,UAAU,OAAO;AACpB,iBAAW,UAAU;AACrB,UAAI,MAAM;AACR,aAAK,YAAY;AAAA,MACnB;AACA,qBAAe,WAAW,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,CAAC,MAAM,CAAC;AAC9G,kBAAY;AACZ,cAAQ,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,CAAC,CAAC;AAAA,IACxF;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,0BAAgB,WAAY;AAC1B,QAAI,WAAW,SAAS;AACtB;AAAA,IACF;AACA,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,QAAI,CAAC,QAAQ;AACX,iBAAW,UAAU;AACrB,aAAO,WAAW,cAAc,CAAC;AAAA,IACnC;AAAA,EACF,GAAG,cAAc,CAAC,GAAG,OAAO,WAAW,GAAG,KAAK,CAAC;AAChD,SAAO,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG;AAAA,IACpC,YAAY;AAAA,MACV,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,qBAAqB;AAAA,MACtG,SAAS,OAAO;AAAA,MAChB,UAAU,sBAAc,aAAa;AAAA,MACrC,YAAY;AAAA,QACV,SAAS,OAAO,WAAW;AAAA,QAC3B,UAAU,OAAO,WAAW;AAAA,QAC5B,OAAO,OAAO,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ,sBAAc,MAAM;AAAA,MAC5B;AAAA,MACA,YAAY,sBAAc,UAAU;AAAA,MACpC,OAAO,sBAAc,KAAK;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AACA,IAAO,uBAAQ;;;AiCnPf,IAAAG,iBAA0B;AAE1B,SAAS,iBAAiB,KAAK;AAC7B,SAAO,WAAW,IAAI,OAAO,aAAa,CAAC;AAC7C;AACA,SAAS,eAAe,QAAQ,MAAM;AACpC,gCAAU,WAAY;AACpB,QAAI,IAAI,OAAO;AACf,QAAI,YAAY;AAChB,aAAS,UAAU;AACjB,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AACjD,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACrC,kBAAQ,GAAG,OAAO;AAAA,YAChB,KAAK;AACH,kBAAI,CAAC,iBAAiB,CAAC;AAAG,uBAAO,CAAC,GAAa,CAAC;AAChD,iBAAG,QAAQ;AAAA,YACb,KAAK;AACH,kBAAI;AAAO,uBAAO,CAAC,GAAa,CAAC;AACjC,qBAAO,CAAC,GAAa,EAAE,KAAK,CAAC;AAAA,YAC/B,KAAK;AACH,uBAAS,GAAG,KAAK;AACjB,kBAAI,OAAO,QAAQ,WAAW;AAC5B,uBAAO,CAAC,GAAa,CAAC;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,CAAC;AAAA,YACxB,KAAK;AACH,qBAAO,CAAC,GAAa,CAAC;AAAA,YACxB,KAAK;AACH,qBAAO,CAAC,GAAa,CAAC;AAAA,YACxB,KAAK;AACH,iBAAG,KAAK;AACR,iBAAG,QAAQ;AAAA,YACb,KAAK;AACH,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UACxB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,YAAQ;AACR,WAAO,WAAY;AACjB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,IAAI;AACT;AACA,IAAO,yBAAQ;;;AC7Cf,IAAAC,iBAAwB;;;ACAxB,IAAAC,iBAAkC;AAClC,SAAS,UAAU,cAAc,cAAc;AAC7C,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,KAAK,WAAO,yBAAS,YAAY,GAAG,CAAC,GACvC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,cAAU,wBAAQ,WAAY;AAChC,QAAI,qBAAqB,iBAAiB,SAAY,CAAC,eAAe;AACtE,QAAI,SAAS,WAAY;AACvB,aAAO,SAAS,SAAU,GAAG;AAC3B,eAAO,MAAM,eAAe,qBAAqB;AAAA,MACnD,CAAC;AAAA,IACH;AACA,QAAI,MAAM,SAAU,OAAO;AACzB,aAAO,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,UAAU,WAAY;AACxB,aAAO,SAAS,YAAY;AAAA,IAC9B;AACA,QAAI,WAAW,WAAY;AACzB,aAAO,SAAS,kBAAkB;AAAA,IACpC;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAGF,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,OAAO,OAAO;AACxB;AACA,IAAO,oBAAQ;;;ADjCA,SAAR,WAA4B,cAAc;AAC/C,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,KAAK,OAAO,kBAAU,CAAC,CAAC,YAAY,GAAG,CAAC,GAC1C,QAAQ,GAAG,CAAC,GACZ,KAAK,GAAG,CAAC,GACT,SAAS,GAAG,QACZ,MAAM,GAAG;AACX,MAAI,cAAU,wBAAQ,WAAY;AAChC,QAAI,UAAU,WAAY;AACxB,aAAO,IAAI,IAAI;AAAA,IACjB;AACA,QAAI,WAAW,WAAY;AACzB,aAAO,IAAI,KAAK;AAAA,IAClB;AACA,WAAO;AAAA,MACL;AAAA,MACA,KAAK,SAAU,GAAG;AAChB,eAAO,IAAI,CAAC,CAAC,CAAC;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,OAAO,OAAO;AACxB;;;AE3BO,SAAS,iBAAiB,QAAQ,gBAAgB;AACvD,MAAI,CAAC,mBAAW;AACd,WAAO;AAAA,EACT;AACA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,WAAW,MAAM,GAAG;AACtB,oBAAgB,OAAO;AAAA,EACzB,WAAW,aAAa,QAAQ;AAC9B,oBAAgB,OAAO;AAAA,EACzB,OAAO;AACL,oBAAgB;AAAA,EAClB;AACA,SAAO;AACT;;;ACjBA,IAAI,qBAAqB,SAAU,SAAS;AAC1C,SAAO,QAAQ,MAAM,SAAU,MAAM;AACnC,QAAI,gBAAgB,iBAAiB,IAAI;AACzC,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,YAAY,aAAa,YAAY;AACrD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,IAAI,YAAY,SAAU,MAAM;AAC9B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,KAAK,YAAY;AAC1B;AACA,IAAI,sBAAsB,SAAU,QAAQ;AAC1C,MAAI,CAAC,UAAU,CAAC,SAAS,aAAa;AACpC,WAAO;AAAA,EACT;AACA,MAAI,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACtD,MAAI,mBAAmB,OAAO,GAAG;AAC/B,WAAO,UAAU,iBAAiB,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC/C;AACA,SAAO;AACT;AACA,IAAO,8BAAQ;;;AC7Bf,IAAAC,iBAA0B;;;ACA1B,IAAAC,iBAAuB;AAIvB,IAAI,yBAAyB,SAAU,eAAe;AAOpD,MAAIC,uBAAsB,SAAU,QAAQ,MAAM,QAAQ;AACxD,QAAI,iBAAa,uBAAO,KAAK;AAC7B,QAAI,qBAAiB,uBAAO,CAAC,CAAC;AAC9B,QAAI,kBAAc,uBAAO,CAAC,CAAC;AAC3B,QAAI,gBAAY,uBAAO,MAAS;AAChC,kBAAc,WAAY;AACxB,UAAI;AACJ,UAAI,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACtD,UAAI,MAAM,QAAQ,IAAI,SAAU,MAAM;AACpC,eAAO,iBAAiB,IAAI;AAAA,MAC9B,CAAC;AAED,UAAI,CAAC,WAAW,SAAS;AACvB,mBAAW,UAAU;AACrB,uBAAe,UAAU;AACzB,oBAAY,UAAU;AACtB,kBAAU,UAAU,OAAO;AAC3B;AAAA,MACF;AACA,UAAI,IAAI,WAAW,eAAe,QAAQ,UAAU,CAAC,oBAAY,eAAe,SAAS,GAAG,KAAK,CAAC,oBAAY,YAAY,SAAS,IAAI,GAAG;AACxI,SAAC,KAAK,UAAU,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS;AAC/E,uBAAe,UAAU;AACzB,oBAAY,UAAU;AACtB,kBAAU,UAAU,OAAO;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,uBAAW,WAAY;AACrB,UAAI;AACJ,OAAC,KAAK,UAAU,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS;AAE/E,iBAAW,UAAU;AAAA,IACvB,CAAC;AAAA,EACH;AACA,SAAOA;AACT;AACA,IAAO,iCAAQ;;;AD5Cf,IAAI,sBAAsB,+BAAuB,wBAAS;AAC1D,IAAO,8BAAQ;;;AECA,SAAR,aAA8B,aAAa,QAAQ,WAAW;AACnE,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AACA,MAAI,iBAAiB,kBAAU,WAAW;AAC1C,8BAAoB,WAAY;AAC9B,QAAI,UAAU,SAAU,OAAO;AAC7B,UAAI,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACtD,UAAI,QAAQ,KAAK,SAAU,MAAM;AAC/B,YAAI,gBAAgB,iBAAiB,IAAI;AACzC,eAAO,CAAC,iBAAiB,cAAc,SAAS,MAAM,MAAM;AAAA,MAC9D,CAAC,GAAG;AACF;AAAA,MACF;AACA,qBAAe,QAAQ,KAAK;AAAA,IAC9B;AACA,QAAI,mBAAmB,4BAAoB,MAAM;AACjD,QAAI,aAAa,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AAClE,eAAW,QAAQ,SAAU,OAAO;AAClC,aAAO,iBAAiB,iBAAiB,OAAO,OAAO;AAAA,IACzD,CAAC;AACD,WAAO,WAAY;AACjB,iBAAW,QAAQ,SAAU,OAAO;AAClC,eAAO,iBAAiB,oBAAoB,OAAO,OAAO;AAAA,MAC5D,CAAC;AAAA,IACH;AAAA,EACF,GAAG,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS,GAAG,MAAM;AAC/D;;;AC9BA,IAAAC,iBAAgC;AAIhC,SAAS,qBAAqB,cAAc,SAAS;AACnD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,QAAQ,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe,CAAC;AAC/E,MAAI,eAAe,QAAQ,cACzB,KAAK,QAAQ,sBACb,uBAAuB,OAAO,SAAS,iBAAiB,IACxD,KAAK,QAAQ,eACb,gBAAgB,OAAO,SAAS,UAAU,IAC1C,KAAK,QAAQ,SACbC,WAAU,OAAO,SAAS,aAAa;AACzC,MAAI,QAAQ,MAAM,aAAa;AAC/B,MAAI,eAAe,OAAO,UAAU,eAAe,KAAK,OAAO,aAAa;AAC5E,MAAI,mBAAe,wBAAQ,WAAY;AACrC,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,oBAAoB,GAAG;AACrE,aAAO,MAAM,oBAAoB;AAAA,IACnC;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,eAAW,uBAAO,YAAY;AAClC,MAAI,cAAc;AAChB,aAAS,UAAU;AAAA,EACrB;AACA,MAAI,SAAS,kBAAU;AACvB,WAAS,SAAS,GAAG;AACnB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IAC7B;AACA,QAAI,IAAI,WAAW,CAAC,IAAI,EAAE,SAAS,OAAO,IAAI;AAC9C,QAAI,CAAC,cAAc;AACjB,eAAS,UAAU;AACnB,aAAO;AAAA,IACT;AACA,QAAI,MAAMA,QAAO,GAAG;AAClB,YAAMA,QAAO,EAAE,MAAM,OAAO,cAAc,CAAC,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAAA,IACrE;AAAA,EACF;AACA,SAAO,CAAC,SAAS,SAAS,sBAAc,QAAQ,CAAC;AACnD;AACA,IAAO,+BAAQ;;;AC/Cf,SAAS,OAAQ,QAAQ;AACvB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC;AACxB,aAAS,OAAO,QAAQ;AACtB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAIA,IAAI,mBAAmB;AAAA,EACrB,MAAM,SAAU,OAAO;AACrB,QAAI,MAAM,CAAC,MAAM,KAAK;AACpB,cAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,IAC3B;AACA,WAAO,MAAM,QAAQ,oBAAoB,kBAAkB;AAAA,EAC7D;AAAA,EACA,OAAO,SAAU,OAAO;AACtB,WAAO,mBAAmB,KAAK,EAAE;AAAA,MAC/B;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAKA,SAAS,KAAM,WAAW,mBAAmB;AAC3C,WAAS,IAAK,MAAM,OAAO,YAAY;AACrC,QAAI,OAAO,aAAa,aAAa;AACnC;AAAA,IACF;AAEA,iBAAa,OAAO,CAAC,GAAG,mBAAmB,UAAU;AAErD,QAAI,OAAO,WAAW,YAAY,UAAU;AAC1C,iBAAW,UAAU,IAAI,KAAK,KAAK,IAAI,IAAI,WAAW,UAAU,KAAK;AAAA,IACvE;AACA,QAAI,WAAW,SAAS;AACtB,iBAAW,UAAU,WAAW,QAAQ,YAAY;AAAA,IACtD;AAEA,WAAO,mBAAmB,IAAI,EAC3B,QAAQ,wBAAwB,kBAAkB,EAClD,QAAQ,SAAS,MAAM;AAE1B,QAAI,wBAAwB;AAC5B,aAAS,iBAAiB,YAAY;AACpC,UAAI,CAAC,WAAW,aAAa,GAAG;AAC9B;AAAA,MACF;AAEA,+BAAyB,OAAO;AAEhC,UAAI,WAAW,aAAa,MAAM,MAAM;AACtC;AAAA,MACF;AASA,+BAAyB,MAAM,WAAW,aAAa,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,IACvE;AAEA,WAAQ,SAAS,SACf,OAAO,MAAM,UAAU,MAAM,OAAO,IAAI,IAAI;AAAA,EAChD;AAEA,WAAS,IAAK,MAAM;AAClB,QAAI,OAAO,aAAa,eAAgB,UAAU,UAAU,CAAC,MAAO;AAClE;AAAA,IACF;AAIA,QAAI,UAAU,SAAS,SAAS,SAAS,OAAO,MAAM,IAAI,IAAI,CAAC;AAC/D,QAAI,MAAM,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,QAAQ,QAAQ,CAAC,EAAE,MAAM,GAAG;AAChC,UAAI,QAAQ,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAEnC,UAAI;AACF,YAAI,QAAQ,mBAAmB,MAAM,CAAC,CAAC;AACvC,YAAI,KAAK,IAAI,UAAU,KAAK,OAAO,KAAK;AAExC,YAAI,SAAS,OAAO;AAClB;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AAAA,MAAC;AAAA,IACf;AAEA,WAAO,OAAO,IAAI,IAAI,IAAI;AAAA,EAC5B;AAEA,SAAO,OAAO;AAAA,IACZ;AAAA,MACE;AAAA,MACA;AAAA,MACA,QAAQ,SAAU,MAAM,YAAY;AAClC;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,CAAC,GAAG,YAAY;AAAA,YACrB,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,gBAAgB,SAAU,YAAY;AACpC,eAAO,KAAK,KAAK,WAAW,OAAO,CAAC,GAAG,KAAK,YAAY,UAAU,CAAC;AAAA,MACrE;AAAA,MACA,eAAe,SAAUC,YAAW;AAClC,eAAO,KAAK,OAAO,CAAC,GAAG,KAAK,WAAWA,UAAS,GAAG,KAAK,UAAU;AAAA,MACpE;AAAA,IACF;AAAA,IACA;AAAA,MACE,YAAY,EAAE,OAAO,OAAO,OAAO,iBAAiB,EAAE;AAAA,MACtD,WAAW,EAAE,OAAO,OAAO,OAAO,SAAS,EAAE;AAAA,IAC/C;AAAA,EACF;AACF;AAEA,IAAI,MAAM,KAAK,kBAAkB,EAAE,MAAM,IAAI,CAAC;;;AChI9C,IAAAC,iBAAyB;AAGzB,SAAS,eAAe,WAAW,SAAS;AAC1C,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,KAAK,WAAO,yBAAS,WAAY;AACjC,QAAI,cAAc,IAAQ,IAAI,SAAS;AACvC,QAAI,SAAS,WAAW,GAAG;AACzB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ,YAAY,GAAG;AACpC,aAAO,QAAQ,aAAa;AAAA,IAC9B;AACA,WAAO,QAAQ;AAAA,EACjB,CAAC,GAAG,CAAC,GACL,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,cAAc,sBAAc,SAAU,UAAU,YAAY;AAC9D,QAAI,eAAe,QAAQ;AACzB,mBAAa,CAAC;AAAA,IAChB;AAEA,QAAIC,MAAK,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG,UAAU,GACjD,eAAeA,IAAG,cAClB,cAAc,OAAOA,KAAI,CAAC,cAAc,CAAC;AAC3C,QAAI,QAAQ,WAAW,QAAQ,IAAI,SAAS,KAAK,IAAI;AACrD,aAAS,KAAK;AACd,QAAI,UAAU,QAAW;AACvB,UAAQ,OAAO,SAAS;AAAA,IAC1B,OAAO;AACL,UAAQ,IAAI,WAAW,OAAO,WAAW;AAAA,IAC3C;AAAA,EACF,CAAC;AACD,SAAO,CAAC,OAAO,WAAW;AAC5B;AACA,IAAO,yBAAQ;;;ACtCf,mBAAkB;AAClB,IAAAC,iBAA6C;AAG7C,IAAI,WAAW,SAAU,QAAQ;AAC/B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,MAAI,WAAO,aAAAC,SAAM,MAAM,EAAE,QAAQ,IAAI,KAAK,IAAI;AAC9C,SAAO,OAAO,IAAI,IAAI;AACxB;AACA,IAAI,UAAU,SAAU,cAAc;AACpC,SAAO;AAAA,IACL,MAAM,KAAK,MAAM,eAAe,KAAQ;AAAA,IACxC,OAAO,KAAK,MAAM,eAAe,IAAO,IAAI;AAAA,IAC5C,SAAS,KAAK,MAAM,eAAe,GAAK,IAAI;AAAA,IAC5C,SAAS,KAAK,MAAM,eAAe,GAAI,IAAI;AAAA,IAC3C,cAAc,KAAK,MAAM,YAAY,IAAI;AAAA,EAC3C;AACF;AACA,IAAI,eAAe,SAAU,SAAS;AACpC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,KAAK,WAAW,CAAC,GACnB,WAAW,GAAG,UACd,aAAa,GAAG,YAChB,KAAK,GAAG,UACR,WAAW,OAAO,SAAS,MAAO,IAClC,QAAQ,GAAG;AACb,MAAI,mBAAe,wBAAQ,WAAY;AACrC,WAAO,SAAS,QAAQ,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,WAAW;AAAA,EACtE,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,SAAS,cAAc,UAAU,eAAe;AACpD,MAAI,KAAK,WAAO,yBAAS,WAAY;AACjC,WAAO,SAAS,MAAM;AAAA,EACxB,CAAC,GAAG,CAAC,GACL,WAAW,GAAG,CAAC,GACf,cAAc,GAAG,CAAC;AACpB,MAAI,WAAW,kBAAU,KAAK;AAC9B,gCAAU,WAAY;AACpB,QAAI,CAAC,QAAQ;AAEX,kBAAY,CAAC;AACb;AAAA,IACF;AAEA,gBAAY,SAAS,MAAM,CAAC;AAC5B,QAAI,QAAQ,YAAY,WAAY;AAClC,UAAIC;AACJ,UAAI,aAAa,SAAS,MAAM;AAChC,kBAAY,UAAU;AACtB,UAAI,eAAe,GAAG;AACpB,sBAAc,KAAK;AACnB,SAACA,MAAK,SAAS,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,QAAQ;AAAA,MAC/E;AAAA,IACF,GAAG,QAAQ;AACX,WAAO,WAAY;AACjB,aAAO,cAAc,KAAK;AAAA,IAC5B;AAAA,EACF,GAAG,CAAC,QAAQ,QAAQ,CAAC;AACrB,MAAI,mBAAe,wBAAQ,WAAY;AACrC,WAAO,QAAQ,QAAQ;AAAA,EACzB,GAAG,CAAC,QAAQ,CAAC;AACb,SAAO,CAAC,UAAU,YAAY;AAChC;AACA,IAAO,uBAAQ;;;ACnEf,IAAAC,iBAAyB;AAGzB,SAAS,eAAe,KAAK,SAAS;AACpC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,MAAM,QAAQ,KAChB,MAAM,QAAQ;AAChB,MAAI,SAAS;AACb,MAAI,SAAS,GAAG,GAAG;AACjB,aAAS,KAAK,IAAI,KAAK,MAAM;AAAA,EAC/B;AACA,MAAI,SAAS,GAAG,GAAG;AACjB,aAAS,KAAK,IAAI,KAAK,MAAM;AAAA,EAC/B;AACA,SAAO;AACT;AACA,SAAS,WAAW,cAAc,SAAS;AACzC,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,MAAM,QAAQ,KAChB,MAAM,QAAQ;AAChB,MAAI,KAAK,WAAO,yBAAS,WAAY;AACjC,WAAO,eAAe,cAAc;AAAA,MAClC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,CAAC,GACL,UAAU,GAAG,CAAC,GACd,aAAa,GAAG,CAAC;AACnB,MAAI,WAAW,SAAU,OAAO;AAC9B,eAAW,SAAU,GAAG;AACtB,UAAI,SAAS,SAAS,KAAK,IAAI,QAAQ,MAAM,CAAC;AAC9C,aAAO,eAAe,QAAQ;AAAA,QAC5B;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,MAAI,MAAM,SAAU,OAAO;AACzB,QAAI,UAAU,QAAQ;AACpB,cAAQ;AAAA,IACV;AACA,aAAS,SAAU,GAAG;AACpB,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH;AACA,MAAI,MAAM,SAAU,OAAO;AACzB,QAAI,UAAU,QAAQ;AACpB,cAAQ;AAAA,IACV;AACA,aAAS,SAAU,GAAG;AACpB,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH;AACA,MAAI,MAAM,SAAU,OAAO;AACzB,aAAS,KAAK;AAAA,EAChB;AACA,MAAI,QAAQ,WAAY;AACtB,aAAS,YAAY;AAAA,EACvB;AACA,SAAO,CAAC,SAAS;AAAA,IACf,KAAK,sBAAc,GAAG;AAAA,IACtB,KAAK,sBAAc,GAAG;AAAA,IACtB,KAAK,sBAAc,GAAG;AAAA,IACtB,OAAO,sBAAc,KAAK;AAAA,EAC5B,CAAC;AACH;AACA,IAAO,qBAAQ;;;ACzEf,IAAAC,iBAAoC;;;ACDpC,IAAAC,mBAAqB;AACrB,SAAS,cAAc;AACrB,MAAI,cAAc,OAAO,WAAW,cAAc,cAAc,OAAO,WAAW,YAAY,UAAU,OAAO,WAAW,UAAU;AACpI,MAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU;AAC5E,SAAO,cAAc;AACvB;AACA,IAAI,CAAC,YAAY,GAAG;AAClB,SAAO,OAAO;AAChB;;;ACNA,IAAAC,iBAAwB;AAKxB,SAAS,cAAc,IAAI,SAAS;AAClC,MAAI;AACJ,MAAI,eAAO;AACT,QAAI,CAAC,WAAW,EAAE,GAAG;AACnB,cAAQ,MAAM,uDAAuD,OAAO,OAAO,EAAE,CAAC;AAAA,IACxF;AAAA,EACF;AACA,MAAI,QAAQ,kBAAU,EAAE;AACxB,MAAI,QAAQ,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK;AAClH,MAAI,gBAAY,wBAAQ,WAAY;AAClC,eAAO,iBAAAC,SAAS,WAAY;AAC1B,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MACzB;AACA,aAAO,MAAM,QAAQ,MAAM,OAAO,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAAA,IAC1E,GAAG,MAAM,OAAO;AAAA,EAClB,GAAG,CAAC,CAAC;AACL,qBAAW,WAAY;AACrB,cAAU,OAAO;AAAA,EACnB,CAAC;AACD,SAAO;AAAA,IACL,KAAK;AAAA,IACL,QAAQ,UAAU;AAAA,IAClB,OAAO,UAAU;AAAA,EACnB;AACF;AACA,IAAO,wBAAQ;;;AF/Bf,SAAS,YAAY,OAAO,SAAS;AACnC,MAAI,KAAK,WAAO,yBAAS,KAAK,GAAG,CAAC,GAChC,YAAY,GAAG,CAAC,GAChB,eAAe,GAAG,CAAC;AACrB,MAAI,MAAM,sBAAc,WAAY;AAClC,iBAAa,KAAK;AAAA,EACpB,GAAG,OAAO,EAAE;AACZ,gCAAU,WAAY;AACpB,QAAI;AAAA,EACN,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AACA,IAAO,sBAAQ;;;AGdf,IAAAC,iBAAoC;AAGpC,SAAS,kBAAkB,QAAQ,MAAM,SAAS;AAChD,MAAI,KAAK,WAAO,yBAAS,CAAC,CAAC,GAAG,CAAC,GAC7B,OAAO,GAAG,CAAC,GACX,UAAU,GAAG,CAAC;AAChB,MAAI,MAAM,sBAAc,WAAY;AAClC,YAAQ,CAAC,CAAC;AAAA,EACZ,GAAG,OAAO,EAAE;AACZ,gCAAU,WAAY;AACpB,WAAO,IAAI;AAAA,EACb,GAAG,IAAI;AACP,0BAAgB,QAAQ,CAAC,IAAI,CAAC;AAChC;AACA,IAAO,4BAAQ;;;AChBf,IAAAC,iBAA0B;;;ACA1B,IAAAC,iBAAuB;;;ACAvB,gCAAoB;AACb,IAAI,YAAY,SAAU,OAAO,OAAO;AAC7C,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,aAAO,0BAAAC,SAAQ,OAAO,KAAK;AAC7B;;;ADPO,IAAI,0BAA0B,SAAU,MAAM;AACnD,SAAO,SAAU,QAAQ,MAAM;AAC7B,QAAI,UAAM,uBAAO,MAAS;AAC1B,QAAI,gBAAY,uBAAO,CAAC;AACxB,QAAI,SAAS,UAAa,CAAC,UAAU,MAAM,IAAI,OAAO,GAAG;AACvD,gBAAU,WAAW;AAAA,IACvB;AACA,QAAI,UAAU;AACd,SAAK,QAAQ,CAAC,UAAU,OAAO,CAAC;AAAA,EAClC;AACF;;;ADVA,IAAO,+BAAQ,wBAAwB,wBAAS;;;AGFhD,IAAAC,iBAAgC;AAEhC,IAAO,qCAAQ,wBAAwB,8BAAe;;;ACDtD,IAAAC,iBAAyB;;;ACEzB,SAAS,iBAAiB,WAAW,SAAS,SAAS;AACrD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,KAAK,QAAQ,QACf,SAAS,OAAO,SAAS,OAAO;AAClC,MAAI,aAAa,kBAAU,OAAO;AAClC,8BAAoB,WAAY;AAC9B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,QAAI,gBAAgB,iBAAiB,QAAQ,QAAQ,MAAM;AAC3D,QAAI,EAAE,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,mBAAmB;AACnG;AAAA,IACF;AACA,QAAI,gBAAgB,SAAU,OAAO;AACnC,aAAO,WAAW,QAAQ,KAAK;AAAA,IACjC;AACA,QAAI,iBAAiB,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AACtE,mBAAe,QAAQ,SAAU,OAAO;AACtC,oBAAc,iBAAiB,OAAO,eAAe;AAAA,QACnD,SAAS,QAAQ;AAAA,QACjB,MAAM,QAAQ;AAAA,QACd,SAAS,QAAQ;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AACD,WAAO,WAAY;AACjB,qBAAe,QAAQ,SAAU,OAAO;AACtC,sBAAc,oBAAoB,OAAO,eAAe;AAAA,UACtD,SAAS,QAAQ;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,WAAW,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS,MAAM,GAAG,QAAQ,MAAM;AACxF;AACA,IAAO,2BAAQ;;;ADlCf,IAAI,gBAAgB,WAAY;AAC9B,MAAI,CAAC,mBAAW;AACd,WAAO;AAAA,EACT;AACA,SAAO,SAAS;AAClB;AACA,SAAS,wBAAwB;AAC/B,MAAI,KAAK,WAAO,yBAAS,aAAa,GAAG,CAAC,GACxC,qBAAqB,GAAG,CAAC,GACzB,wBAAwB,GAAG,CAAC;AAC9B,2BAAiB,oBAAoB,WAAY;AAC/C,0BAAsB,cAAc,CAAC;AAAA,EACvC,GAAG;AAAA,IACD,QAAQ,WAAY;AAClB,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAO,gCAAQ;;;AEvBf,IAAAC,iBAAuB;AAMvB,IAAI,UAAU,SAAU,MAAM,QAAQ,SAAS;AAC7C,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,aAAa,kBAAU,OAAO;AAClC,MAAI,UAAU,kBAAU,IAAI;AAC5B,MAAI,sBAAkB,uBAAO,MAAS;AACtC,MAAI,YAAY,WAAW,QAAQ;AACnC,mBAAS,WAAY;AACnB,QAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,OAAO;AACzE,UAAI,QAAQ,UAAU;AACtB,UAAI,SAAS,KAAK,GAAG;AACnB,YAAI,eAAe,IAAI,MAAM;AAC7B,qBAAa,MAAM;AACnB,wBAAgB,UAAU;AAAA,MAC5B,OAAO;AACL,wBAAgB,UAAU;AAAA,MAC5B;AAAA,IACF;AAAA,EACF,CAAC;AACD,8BAAoB,WAAY;AAC9B,QAAI,gBAAgB,iBAAiB,MAAM;AAC3C,QAAI,EAAE,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,mBAAmB;AACnG;AAAA,IACF;AACA,QAAI,cAAc,SAAU,OAAO;AACjC,UAAI,IAAI;AACR,OAAC,MAAM,KAAK,WAAW,SAAS,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK;AACnG,YAAM,aAAa,QAAQ,UAAU,KAAK,UAAU,QAAQ,OAAO,CAAC;AACpE,WAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,UAAU,gBAAgB,SAAS;AACtG,YAAI,KAAK,UAAU,SACjB,UAAU,OAAO,SAAS,IAAI,IAC9B,KAAK,UAAU,SACf,UAAU,OAAO,SAAS,IAAI;AAChC,cAAM,aAAa,aAAa,gBAAgB,SAAS,SAAS,OAAO;AAAA,MAC3E;AAAA,IACF;AACA,QAAI,YAAY,SAAU,OAAO;AAC/B,UAAI,IAAI;AACR,OAAC,MAAM,KAAK,WAAW,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK;AAAA,IACnG;AACA,kBAAc,aAAa,aAAa,MAAM;AAC9C,kBAAc,iBAAiB,aAAa,WAAW;AACvD,kBAAc,iBAAiB,WAAW,SAAS;AACnD,WAAO,WAAY;AACjB,oBAAc,oBAAoB,aAAa,WAAW;AAC1D,oBAAc,oBAAoB,WAAW,SAAS;AAAA,IACxD;AAAA,EACF,GAAG,CAAC,GAAG,MAAM;AACf;AACA,IAAO,kBAAQ;;;ACrDf,IAAAC,iBAAuB;AACvB,IAAI,UAAU,SAAU,QAAQ,SAAS;AACvC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,aAAa,kBAAU,OAAO;AAElC,MAAI,sBAAkB,uBAAO,MAAS;AACtC,8BAAoB,WAAY;AAC9B,QAAI,gBAAgB,iBAAiB,MAAM;AAC3C,QAAI,EAAE,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,mBAAmB;AACnG;AAAA,IACF;AACA,QAAI,SAAS,SAAU,cAAc,OAAO;AAC1C,UAAI,MAAM,aAAa,QAAQ,eAAe;AAC9C,UAAI,MAAM,aAAa,QAAQ,QAAQ;AACvC,UAAI,OAAO,WAAW,QAAQ,OAAO;AACnC,YAAI,OAAO;AACX,YAAI;AACF,iBAAO,KAAK,MAAM,GAAG;AAAA,QACvB,SAAS,IAAI;AACX,iBAAO;AAAA,QACT;AACA,mBAAW,QAAQ,MAAM,MAAM,KAAK;AACpC;AAAA,MACF;AACA,UAAI,OAAO,WAAW,QAAQ,OAAO;AACnC,mBAAW,QAAQ,MAAM,KAAK,KAAK;AACnC;AAAA,MACF;AACA,UAAI,aAAa,SAAS,aAAa,MAAM,UAAU,WAAW,QAAQ,SAAS;AACjF,mBAAW,QAAQ,QAAQ,MAAM,KAAK,aAAa,KAAK,GAAG,KAAK;AAChE;AAAA,MACF;AACA,UAAI,aAAa,SAAS,aAAa,MAAM,UAAU,WAAW,QAAQ,QAAQ;AAChF,qBAAa,MAAM,CAAC,EAAE,YAAY,SAAU,MAAM;AAChD,qBAAW,QAAQ,OAAO,MAAM,KAAK;AAAA,QACvC,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,cAAc,SAAU,OAAO;AACjC,UAAI,IAAI;AACR,YAAM,eAAe;AACrB,YAAM,gBAAgB;AACtB,sBAAgB,UAAU,MAAM;AAChC,OAAC,MAAM,KAAK,WAAW,SAAS,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK;AAAA,IACrG;AACA,QAAI,aAAa,SAAU,OAAO;AAChC,UAAI,IAAI;AACR,YAAM,eAAe;AACrB,OAAC,MAAM,KAAK,WAAW,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK;AAAA,IACpG;AACA,QAAI,cAAc,SAAU,OAAO;AACjC,UAAI,IAAI;AACR,UAAI,MAAM,WAAW,gBAAgB,SAAS;AAC5C,SAAC,MAAM,KAAK,WAAW,SAAS,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK;AAAA,MACrG;AAAA,IACF;AACA,QAAI,SAAS,SAAU,OAAO;AAC5B,UAAI,IAAI;AACR,YAAM,eAAe;AACrB,aAAO,MAAM,cAAc,KAAK;AAChC,OAAC,MAAM,KAAK,WAAW,SAAS,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK;AAAA,IAChG;AACA,QAAI,UAAU,SAAU,OAAO;AAC7B,UAAI,IAAI;AACR,aAAO,MAAM,eAAe,KAAK;AACjC,OAAC,MAAM,KAAK,WAAW,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK;AAAA,IACjG;AACA,kBAAc,iBAAiB,aAAa,WAAW;AACvD,kBAAc,iBAAiB,YAAY,UAAU;AACrD,kBAAc,iBAAiB,aAAa,WAAW;AACvD,kBAAc,iBAAiB,QAAQ,MAAM;AAC7C,kBAAc,iBAAiB,SAAS,OAAO;AAC/C,WAAO,WAAY;AACjB,oBAAc,oBAAoB,aAAa,WAAW;AAC1D,oBAAc,oBAAoB,YAAY,UAAU;AACxD,oBAAc,oBAAoB,aAAa,WAAW;AAC1D,oBAAc,oBAAoB,QAAQ,MAAM;AAChD,oBAAc,oBAAoB,SAAS,OAAO;AAAA,IACpD;AAAA,EACF,GAAG,CAAC,GAAG,MAAM;AACf;AACA,IAAO,kBAAQ;;;ACrFf,IAAAC,iBAA8C;AAE9C,IAAI,iBAAiB,SAAU,aAAa;AAC1C,MAAI,gBAAgB,QAAQ;AAC1B,kBAAc,CAAC;AAAA,EACjB;AACA,MAAI,iBAAa,uBAAO,EAAE;AAC1B,MAAI,cAAU,uBAAO,CAAC,CAAC;AACvB,MAAI,aAAS,4BAAY,SAAU,OAAO;AACxC,eAAW,WAAW;AACtB,YAAQ,QAAQ,OAAO,OAAO,GAAG,WAAW,OAAO;AAAA,EACrD,GAAG,CAAC,CAAC;AACL,MAAI,KAAK,WAAO,yBAAS,WAAY;AACjC,gBAAY,QAAQ,SAAU,GAAG,OAAO;AACtC,aAAO,KAAK;AAAA,IACd,CAAC;AACD,WAAO;AAAA,EACT,CAAC,GAAG,CAAC,GACL,OAAO,GAAG,CAAC,GACX,UAAU,GAAG,CAAC;AAChB,MAAI,gBAAY,4BAAY,SAAU,SAAS;AAC7C,YAAQ,UAAU,CAAC;AACnB,YAAQ,WAAY;AAClB,cAAQ,QAAQ,SAAU,GAAG,OAAO;AAClC,eAAO,KAAK;AAAA,MACd,CAAC;AACD,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,aAAS,4BAAY,SAAU,OAAO,MAAM;AAC9C,YAAQ,SAAU,GAAG;AACnB,UAAI,OAAO,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK;AAC7C,WAAK,OAAO,OAAO,GAAG,IAAI;AAC1B,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,aAAS,4BAAY,SAAU,OAAO;AACxC,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B,GAAG,CAAC,CAAC;AACL,MAAI,eAAW,4BAAY,SAAU,KAAK;AACxC,WAAO,QAAQ,QAAQ,UAAU,SAAU,KAAK;AAC9C,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,YAAQ,4BAAY,SAAU,OAAO,OAAO;AAC9C,YAAQ,SAAU,GAAG;AACnB,UAAI,OAAO,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK;AAC7C,YAAM,QAAQ,SAAU,GAAG,GAAG;AAC5B,eAAO,QAAQ,CAAC;AAAA,MAClB,CAAC;AACD,WAAK,OAAO,MAAM,MAAM,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO,KAAK,GAAG,KAAK,CAAC;AACvE,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,cAAU,4BAAY,SAAU,OAAO,MAAM;AAC/C,YAAQ,SAAU,GAAG;AACnB,UAAI,OAAO,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK;AAC7C,WAAK,KAAK,IAAI;AACd,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,aAAS,4BAAY,SAAU,OAAO;AACxC,YAAQ,SAAU,GAAG;AACnB,UAAI,OAAO,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK;AAC7C,WAAK,OAAO,OAAO,CAAC;AAEpB,UAAI;AACF,gBAAQ,QAAQ,OAAO,OAAO,CAAC;AAAA,MACjC,SAAS,GAAG;AACV,gBAAQ,MAAM,CAAC;AAAA,MACjB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,kBAAc,4BAAY,SAAU,SAAS;AAC/C,QAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B,UAAI,eAAO;AACT,gBAAQ,MAAM,mFAAoF,OAAO,OAAO,SAAS,IAAK,CAAC;AAAA,MACjI;AACA;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,QAAQ;AACnB;AAAA,IACF;AACA,YAAQ,SAAU,UAAU;AAC1B,UAAI,aAAa,CAAC;AAClB,UAAI,UAAU,SAAS,OAAO,SAAU,MAAM,OAAO;AACnD,YAAI,aAAa,CAAC,QAAQ,SAAS,KAAK;AACxC,YAAI,YAAY;AACd,qBAAW,KAAK,OAAO,KAAK,CAAC;AAAA,QAC/B;AACA,eAAO;AAAA,MACT,CAAC;AACD,cAAQ,UAAU;AAClB,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,WAAO,4BAAY,SAAU,UAAU,UAAU;AACnD,QAAI,aAAa,UAAU;AACzB;AAAA,IACF;AACA,YAAQ,SAAU,GAAG;AACnB,UAAI,UAAU,cAAc,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK;AAChD,UAAI,OAAO,QAAQ,OAAO,SAAU,GAAG,OAAO;AAC5C,eAAO,UAAU;AAAA,MACnB,CAAC;AACD,WAAK,OAAO,UAAU,GAAG,QAAQ,QAAQ,CAAC;AAE1C,UAAI;AACF,YAAI,UAAU,QAAQ,QAAQ,OAAO,SAAU,GAAG,OAAO;AACvD,iBAAO,UAAU;AAAA,QACnB,CAAC;AACD,gBAAQ,OAAO,UAAU,GAAG,QAAQ,QAAQ,QAAQ,CAAC;AACrD,gBAAQ,UAAU;AAAA,MACpB,SAAS,GAAG;AACV,gBAAQ,MAAM,CAAC;AAAA,MACjB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,WAAO,4BAAY,SAAU,MAAM;AACrC,YAAQ,SAAU,GAAG;AACnB,aAAO,EAAE,MAAM;AACf,aAAO,EAAE,OAAO,CAAC,IAAI,CAAC;AAAA,IACxB,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,UAAM,4BAAY,WAAY;AAEhC,QAAI;AACF,cAAQ,UAAU,QAAQ,QAAQ,MAAM,GAAG,QAAQ,QAAQ,SAAS,CAAC;AAAA,IACvE,SAAS,GAAG;AACV,cAAQ,MAAM,CAAC;AAAA,IACjB;AACA,YAAQ,SAAU,GAAG;AACnB,aAAO,EAAE,MAAM,GAAG,EAAE,SAAS,CAAC;AAAA,IAChC,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,cAAU,4BAAY,SAAU,MAAM;AACxC,YAAQ,SAAU,GAAG;AACnB,aAAO,CAAC;AACR,aAAO,CAAC,IAAI,EAAE,OAAO,CAAC;AAAA,IACxB,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,YAAQ,4BAAY,WAAY;AAElC,QAAI;AACF,cAAQ,UAAU,QAAQ,QAAQ,MAAM,GAAG,QAAQ,QAAQ,MAAM;AAAA,IACnE,SAAS,GAAG;AACV,cAAQ,MAAM,CAAC;AAAA,IACjB;AACA,YAAQ,SAAU,GAAG;AACnB,aAAO,EAAE,MAAM,GAAG,EAAE,MAAM;AAAA,IAC5B,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,MAAI,eAAW;AAAA,IAAY,SAAU,QAAQ;AAC3C,aAAO,OAAO,IAAI,SAAU,MAAM,OAAO;AACvC,eAAO;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF,CAAC,EACA,KAAK,SAAU,GAAG,GAAG;AACpB,eAAO,SAAS,EAAE,GAAG,IAAI,SAAS,EAAE,GAAG;AAAA,MACzC,CAAC,EACA,OAAO,SAAU,MAAM;AACtB,eAAO,CAAC,CAAC,KAAK;AAAA,MAChB,CAAC,EACA,IAAI,SAAU,MAAM;AACnB,eAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,CAAC;AAAA,EAAC;AACF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAO,yBAAQ;;;AChMf,IAAAC,iBAAkC;AAClC,IAAI;AAAA;AAAA,EAA4B,WAAY;AAC1C,aAASC,gBAAe;AACtB,UAAI,QAAQ;AACZ,WAAK,gBAAgB,oBAAI,IAAI;AAC7B,WAAK,OAAO,SAAU,KAAK;AACzB,YAAI,KAAK;AACT,YAAI;AACF,mBAAS,KAAK,SAAS,MAAM,aAAa,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AACrF,gBAAI,eAAe,GAAG;AACtB,yBAAa,GAAG;AAAA,UAClB;AAAA,QACF,SAAS,OAAO;AACd,gBAAM;AAAA,YACJ,OAAO;AAAA,UACT;AAAA,QACF,UAAE;AACA,cAAI;AACF,gBAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG;AAAS,iBAAG,KAAK,EAAE;AAAA,UACpD,UAAE;AACA,gBAAI;AAAK,oBAAM,IAAI;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB,SAAU,UAAU;AAEzC,YAAI,kBAAc,uBAAO,MAAS;AAClC,oBAAY,UAAU;AAEtB,sCAAU,WAAY;AACpB,mBAAS,aAAa,KAAK;AACzB,gBAAI,YAAY,SAAS;AACvB,0BAAY,QAAQ,GAAG;AAAA,YACzB;AAAA,UACF;AACA,gBAAM,cAAc,IAAI,YAAY;AACpC,iBAAO,WAAY;AACjB,kBAAM,cAAc,OAAO,YAAY;AAAA,UACzC;AAAA,QACF,GAAG,CAAC,CAAC;AAAA,MACP;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAEF,SAAS,kBAAkB;AACzB,MAAI,UAAM,uBAAO,MAAS;AAC1B,MAAI,CAAC,IAAI,SAAS;AAChB,QAAI,UAAU,IAAI,aAAa;AAAA,EACjC;AACA,SAAO,IAAI;AACb;AACA,IAAO,0BAAQ;;;ACpDf,IAAAC,iBAAsC;AAGtC,SAAS,eAAe,SAAS;AAC/B,MAAI,KAAK,WAAW,CAAC,GACnB,eAAe,GAAG,cAClB,cAAc,GAAG;AACnB,MAAI,KAAK,WAAO,yBAAS,YAAY,GAAG,CAAC,GACvC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,iBAAiB,kBAAU,WAAW;AAC1C,MAAI,YAAQ,4BAAY,WAAY;AAClC,WAAO,SAAS,YAAY;AAAA,EAC9B,GAAG,CAAC,CAAC;AACL,MAAI,eAAW,4BAAY,SAAU,GAAG;AACtC,QAAI,SAAS,EAAE,OAAO;AACtB,QAAI,WAAW,eAAe,OAAO,GAAG;AACtC,aAAO,SAAS,eAAe,QAAQ,MAAM,CAAC;AAAA,IAChD;AAEA,WAAO,SAAS,MAAM;AAAA,EACxB,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,OAAO;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAO,yBAAQ;;;AC3Bf,IAAAC,iBAA4C;AAG5C,IAAI,sBAAsB,CAAC;AAC3B,IAAI,aAAa,SAAU,MAAM,OAAO;AACtC,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,MAAI,SAAS,SAAS,cAAc,eAAgB,OAAO,MAAM,IAAK,CAAC;AACvE,MAAI,CAAC,QAAQ;AACX,QAAI,cAAc,SAAS,cAAc,QAAQ;AACjD,gBAAY,MAAM;AAClB,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,KAAK;AACxC,kBAAY,GAAG,IAAI,MAAM,GAAG;AAAA,IAC9B,CAAC;AACD,gBAAY,aAAa,eAAe,SAAS;AACjD,aAAS,KAAK,YAAY,WAAW;AACrC,WAAO;AAAA,MACL,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AAAA,IACL,KAAK;AAAA,IACL,QAAQ,OAAO,aAAa,aAAa,KAAK;AAAA,EAChD;AACF;AACA,IAAI,UAAU,SAAU,MAAM,OAAO;AACnC,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,MAAI,MAAM,SAAS,cAAc,cAAe,OAAO,MAAM,IAAK,CAAC;AACnE,MAAI,CAAC,KAAK;AACR,QAAI,WAAW,SAAS,cAAc,MAAM;AAC5C,aAAS,MAAM;AACf,aAAS,OAAO;AAChB,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,KAAK;AACxC,eAAS,GAAG,IAAI,MAAM,GAAG;AAAA,IAC3B,CAAC;AAED,QAAI,gBAAgB,eAAe;AAEnC,QAAI,iBAAiB,SAAS,SAAS;AACrC,eAAS,MAAM;AACf,eAAS,KAAK;AAAA,IAChB;AACA,aAAS,aAAa,eAAe,SAAS;AAC9C,aAAS,KAAK,YAAY,QAAQ;AAClC,WAAO;AAAA,MACL,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AAAA,IACL,KAAK;AAAA,IACL,QAAQ,IAAI,aAAa,aAAa,KAAK;AAAA,EAC7C;AACF;AACA,IAAI,cAAc,SAAU,MAAM,SAAS;AACzC,MAAI,KAAK,WAAO,yBAAS,OAAO,YAAY,OAAO,GAAG,CAAC,GACrD,SAAS,GAAG,CAAC,GACb,YAAY,GAAG,CAAC;AAClB,MAAI,UAAM,uBAAO,MAAS;AAC1B,gCAAU,WAAY;AACpB,QAAI,CAAC,MAAM;AACT,gBAAU,OAAO;AACjB;AAAA,IACF;AACA,QAAI,WAAW,KAAK,QAAQ,WAAW,EAAE;AACzC,SAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,SAAS,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS,iBAAiB,KAAK,QAAQ,GAAG;AACtL,UAAI,SAAS,QAAQ,MAAM,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,GAAG;AACxF,UAAI,UAAU,OAAO;AACrB,gBAAU,OAAO,MAAM;AAAA,IACzB,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS,eAAe,KAAK,QAAQ,GAAG;AAC1L,UAAI,SAAS,WAAW,MAAM,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,EAAE;AAC1F,UAAI,UAAU,OAAO;AACrB,gBAAU,OAAO,MAAM;AAAA,IACzB,OAAO;AAEL,cAAQ,MAAM,0JAA+J;AAAA,IAC/K;AACA,QAAI,CAAC,IAAI,SAAS;AAChB;AAAA,IACF;AACA,QAAI,oBAAoB,IAAI,MAAM,QAAW;AAC3C,0BAAoB,IAAI,IAAI;AAAA,IAC9B,OAAO;AACL,0BAAoB,IAAI,KAAK;AAAA,IAC/B;AACA,QAAI,UAAU,SAAU,OAAO;AAC7B,UAAIC;AACJ,UAAI,eAAe,MAAM,SAAS,SAAS,UAAU;AACrD,OAACA,MAAK,IAAI,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,eAAe,YAAY;AACnG,gBAAU,YAAY;AAAA,IACxB;AACA,QAAI,QAAQ,iBAAiB,QAAQ,OAAO;AAC5C,QAAI,QAAQ,iBAAiB,SAAS,OAAO;AAC7C,WAAO,WAAY;AACjB,UAAIA,KAAI,IAAI;AACZ,OAACA,MAAK,IAAI,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,oBAAoB,QAAQ,OAAO;AAC9F,OAAC,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB,SAAS,OAAO;AAC/F,0BAAoB,IAAI,KAAK;AAC7B,UAAI,oBAAoB,IAAI,MAAM,KAAK,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,iBAAiB;AAClH,SAAC,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,MACpE;AACA,UAAI,UAAU;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,SAAO;AACT;AACA,IAAO,sBAAQ;;;AC/Gf,IAAAC,iBAA0B;AAC1B,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAI,aAAa,SAAU,MAAM;AAC/B,gCAAU,WAAY;AACpB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,QAAI,SAAS,KAAK,MAAM,GAAG;AAC3B,QAAI,YAAY,OAAO,OAAO,SAAS,CAAC,EAAE,kBAAkB;AAC5D,QAAI,OAAO,SAAS,cAAc,mBAAmB,KAAK,SAAS,cAAc,MAAM;AACvF,SAAK,OAAO,WAAW,SAAS;AAChC,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,aAAS,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAY,IAAI;AAAA,EAC3D,GAAG,CAAC,IAAI,CAAC;AACX;AACA,IAAO,qBAAQ;;;ACpBf,IAAAC,iBAAyB;AAEV,SAAR,eAAgC,QAAQ,SAAS;AACtD,MAAI,KAAK,WAAO,yBAAS,KAAK,GAAG,CAAC,GAChC,gBAAgB,GAAG,CAAC,GACpB,mBAAmB,GAAG,CAAC;AACzB,MAAI,KAAK,WAAW,CAAC,GACnB,UAAU,GAAG,SACb,SAAS,GAAG,QACZ,WAAW,GAAG;AAChB,2BAAiB,WAAW,SAAU,GAAG;AACvC,QAAI,CAAC,eAAe;AAClB,kBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,CAAC;AAC3D,mBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,IAAI;AACjE,uBAAiB,IAAI;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD;AAAA,EACF,CAAC;AACD,2BAAiB,YAAY,SAAU,GAAG;AACxC,QAAIC,KAAIC;AACR,QAAI,iBAAiB,GAAGA,OAAMD,MAAK,EAAE,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,cAAc,QAAQC,QAAO,SAAS,SAASA,IAAG,KAAKD,KAAI,EAAE,aAAa,IAAI;AACxK,iBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,CAAC;AACxD,mBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,KAAK;AAClE,uBAAiB,KAAK;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;AC9BA,IAAAE,iBAA4C;AAC5C,wBAAuB;AAKvB,IAAI,gBAAgB,SAAU,QAAQ,SAAS;AAC7C,MAAI,KAAK,WAAW,CAAC,GACnB,SAAS,GAAG,QACZ,UAAU,GAAG,SACb,KAAK,GAAG,gBACR,iBAAiB,OAAO,SAAS,QAAQ;AAC3C,MAAI,KAAK,UAAU,cAAc,KAAK,CAAC,iBAAiB,CAAC,IAAI,gBAC3D,KAAK,GAAG,WACR,YAAY,OAAO,SAAS,2BAA2B,IACvD,KAAK,GAAG,QACR,SAAS,OAAO,SAAS,SAAS;AACpC,MAAI,YAAY,kBAAU,MAAM;AAChC,MAAI,aAAa,kBAAU,OAAO;AAGlC,MAAI,KAAK,WAAO,yBAAS,eAAe,GAAG,CAAC,GAC1C,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,eAAW,uBAAO,gBAAgB,CAAC;AACvC,WAAS,kBAAkB;AACzB,WAAO,kBAAAC,QAAW,aAAa,CAAC,CAAC,kBAAAA,QAAW,WAAW,kBAAAA,QAAW,YAAY,iBAAiB,MAAM;AAAA,EACvG;AACA,MAAI,iBAAiB,SAAU,YAAY;AACzC,QAAIC,KAAIC;AACR,QAAI,YAAY;AACd,OAACD,MAAK,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,UAAU;AAAA,IACnF,OAAO;AACL,OAACC,MAAK,UAAU,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,SAAS;AAAA,IACjF;AAAA,EACF;AACA,MAAI,wBAAwB,SAAU,YAAY;AAEhD,QAAI,SAAS,YAAY,YAAY;AACnC,qBAAe,UAAU;AACzB,eAAS,UAAU;AACnB,eAAS,UAAU;AAAA,IACrB;AAAA,EACF;AACA,MAAI,qBAAqB,WAAY;AACnC,QAAI,aAAa,gBAAgB;AACjC,0BAAsB,UAAU;AAAA,EAClC;AACA,MAAI,uBAAuB,SAAU,YAAY;AAC/C,QAAI,KAAK,iBAAiB,MAAM;AAChC,QAAI,CAAC,IAAI;AACP;AAAA,IACF;AACA,QAAI,YAAY,SAAS,eAAe,SAAS;AACjD,QAAI,YAAY;AACd,SAAG,UAAU,IAAI,SAAS;AAC1B,UAAI,CAAC,WAAW;AACd,oBAAY,SAAS,cAAc,OAAO;AAC1C,kBAAU,aAAa,MAAM,SAAS;AACtC,kBAAU,cAAc,gBAAgB,OAAO,WAAW,6JAA6J,EAAE,OAAO,QAAQ,gBAAgB;AACxP,WAAG,YAAY,SAAS;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,SAAG,UAAU,OAAO,SAAS;AAC7B,UAAI,WAAW;AACb,kBAAU,OAAO;AAAA,MACnB;AAAA,IACF;AACA,0BAAsB,UAAU;AAAA,EAClC;AACA,MAAI,kBAAkB,WAAY;AAChC,QAAI,KAAK,iBAAiB,MAAM;AAChC,QAAI,CAAC,IAAI;AACP;AAAA,IACF;AACA,QAAI,gBAAgB;AAClB,2BAAqB,IAAI;AACzB;AAAA,IACF;AACA,QAAI,kBAAAF,QAAW,WAAW;AACxB,UAAI;AACF,0BAAAA,QAAW,QAAQ,EAAE;AAAA,MACvB,SAAS,OAAO;AACd,gBAAQ,MAAM,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,MAAI,iBAAiB,WAAY;AAC/B,QAAI,KAAK,iBAAiB,MAAM;AAChC,QAAI,CAAC,IAAI;AACP;AAAA,IACF;AACA,QAAI,gBAAgB;AAClB,2BAAqB,KAAK;AAC1B;AAAA,IACF;AACA,QAAI,kBAAAA,QAAW,aAAa,kBAAAA,QAAW,YAAY,IAAI;AACrD,wBAAAA,QAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,MAAI,mBAAmB,WAAY;AACjC,QAAI,OAAO;AACT,qBAAe;AAAA,IACjB,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF;AACA,gCAAU,WAAY;AACpB,QAAI,CAAC,kBAAAA,QAAW,aAAa,gBAAgB;AAC3C;AAAA,IACF;AACA,sBAAAA,QAAW,GAAG,UAAU,kBAAkB;AAC1C,WAAO,WAAY;AACjB,wBAAAA,QAAW,IAAI,UAAU,kBAAkB;AAAA,IAC7C;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,OAAO;AAAA,IACb,iBAAiB,sBAAc,eAAe;AAAA,IAC9C,gBAAgB,sBAAc,cAAc;AAAA,IAC5C,kBAAkB,sBAAc,gBAAgB;AAAA,IAChD,WAAW,kBAAAA,QAAW;AAAA,EACxB,CAAC;AACH;AACA,IAAO,wBAAQ;;;AC3HR,IAAI,eAAe,SAAU,OAAO;AACzC,SAAO;AAAA,IACL,kBAAkB,SAAU,MAAM;AAChC,aAAO,MAAM,SAAS,EAAE,SAAS,IAAI;AAAA,IACvC;AAAA,IACA,gBAAgB,MAAM;AAAA,IACtB,gBAAgB,MAAM;AAAA,IACtB,aAAa,MAAM;AAAA,IACnB,gBAAgB,SAAU,QAAQ,UAAU;AAC1C,YAAM,SAAS,QAAQ,QAAQ;AAAA,IACjC;AAAA,EACF;AACF;AACO,IAAI,gBAAgB,SAAU,QAAQ;AAC3C,MAAI,aAAa;AAAA,IACf,YAAY,OAAO,WAAW;AAAA,IAC9B,SAAS,OAAO,WAAW;AAAA,IAC3B,QAAQ,SAAU,WAAW,OAAO;AAClC,UAAI;AACJ,aAAO,WAAW,SAAS;AAAA,QACzB,SAAS,OAAO,WAAW;AAAA,QAC3B,UAAU,OAAO,WAAW;AAAA,MAC9B,IAAI,KAAK,OAAO,OAAO,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AAAA,QAC1E,OAAO;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,UAAU,SAAU,cAAc;AAChC,UAAI;AACJ,aAAO,WAAW,SAAS;AAAA,QACzB,SAAS,OAAO,WAAW;AAAA,QAC3B,UAAU,OAAO,WAAW;AAAA,MAC9B,GAAG,eAAe,KAAK,OAAO,OAAO,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACzF;AAAA,EACF;AACA,MAAI,kBAAkB;AAAA,IACpB,UAAU,OAAO,WAAW;AAAA,IAC5B,kBAAkB,OAAO,WAAW;AAAA,IACpC,SAAS,OAAO,WAAW;AAAA,IAC3B,UAAU,OAAO,WAAW;AAAA,IAC5B,OAAO,OAAO,WAAW;AAAA,EAC3B;AACA,SAAO,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG;AAAA,IACpC;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AC5CA,IAAI,iBAAiB,SAAU,SAAS,SAAS;AAC/C,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,MAAM,qBAAa,SAAS,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG;AAAA,IAC9D,MAAM,QAAQ,QAAQ,aAAa,QAAQ,KAAK,IAAI;AAAA,EACtD,CAAC,CAAC;AACF,SAAO,cAAc,GAAG;AAC1B;AACA,IAAO,yBAAQ;;;ACXf,IAAAG,iBAAsC;AAEtC,SAAS,YAAY,cAAc;AACjC,MAAI,KAAK,WAAO,yBAAS,YAAY,GAAG,CAAC,GACvC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,WAAW,kBAAU,KAAK;AAC9B,MAAI,eAAW,4BAAY,WAAY;AACrC,WAAO,SAAS;AAAA,EAClB,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,OAAO,UAAU,QAAQ;AACnC;AACA,IAAO,sBAAQ;;;ACZf,IAAAC,iBAAiC;AAGjC,IAAI,YAAY,SAAU,MAAM,KAAK;AACnC,MAAI,QAAQ,OAAO,IAAI,OAAO,IAC5B,IAAI,SAAS;AACf,MAAI,SAAS,IAAI,SAAS,GAAG;AAC3B,YAAQ,IAAI,SAAS;AAAA,EACvB;AACA,MAAI,QAAQ,GAAG;AACb,YAAQ;AAAA,EACV;AACA,SAAO;AACT;AACA,IAAI,QAAQ,SAAU,MAAM,WAAW;AACrC,MAAI,QAAQ,UAAU,MAAM,SAAS;AACrC,SAAO;AAAA,IACL,UAAU,UAAU,KAAK;AAAA,IACzB,SAAS,UAAU,MAAM,GAAG,KAAK;AAAA,IACjC,QAAQ,UAAU,MAAM,QAAQ,CAAC;AAAA,EACnC;AACF;AACe,SAAR,iBAAkC,cAAc,WAAW;AAChE,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AACA,MAAI,KAAK,WAAO,yBAAS;AAAA,IACrB,SAAS;AAAA,IACT,MAAM,CAAC;AAAA,IACP,QAAQ,CAAC;AAAA,EACX,CAAC,GAAG,CAAC,GACL,UAAU,GAAG,CAAC,GACd,aAAa,GAAG,CAAC;AACnB,MAAI,UAAU,QAAQ,SACpB,OAAO,QAAQ,MACf,SAAS,QAAQ;AACnB,MAAI,sBAAkB,uBAAO,YAAY;AACzC,MAAI,QAAQ,WAAY;AACtB,QAAI,SAAS,CAAC;AACd,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAO,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,QAAI,WAAW,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI,gBAAgB;AAC/D,oBAAgB,UAAU;AAC1B,eAAW;AAAA,MACT,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,MAAM,CAAC;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,cAAc,SAAU,KAAK;AAC/B,QAAI,QAAQ,cAAc,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,GAAG,KAAK;AAClF,QAAI,eAAe,SAAS,SAAS,IAAI,YAAY,OAAO,SAAS;AAErE,QAAI,eAAe,KAAK,MAAM,SAAS,cAAc;AAEnD,YAAM,OAAO,GAAG,CAAC;AAAA,IACnB;AACA,eAAW;AAAA,MACT,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,MAAI,WAAW,SAAU,MAAM;AAC7B,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW,GAAG;AACvB;AAAA,IACF;AACA,QAAIC,MAAK,MAAM,MAAM,MAAM,GACzB,UAAUA,IAAG,SACb,WAAWA,IAAG,UACd,SAASA,IAAG;AACd,eAAW;AAAA,MACT,MAAM,cAAc,cAAc,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,GAAG,KAAK,GAAG,OAAO,OAAO,GAAG,KAAK;AAAA,MACnH,SAAS;AAAA,MACT,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,MAAI,YAAY,SAAU,MAAM;AAC9B,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,WAAW,GAAG;AACrB;AAAA,IACF;AACA,QAAIA,MAAK,MAAM,MAAM,IAAI,GACvB,UAAUA,IAAG,SACb,WAAWA,IAAG,UACd,SAASA,IAAG;AACd,eAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ,cAAc,cAAc,cAAc,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,OAAO,GAAG,KAAK,GAAG,OAAO,MAAM,GAAG,KAAK;AAAA,IACxH,CAAC;AAAA,EACH;AACA,MAAI,KAAK,SAAU,MAAM;AACvB,QAAI,UAAU,SAAS,IAAI,IAAI,OAAO,OAAO,IAAI;AACjD,QAAI,YAAY,GAAG;AACjB;AAAA,IACF;AACA,QAAI,UAAU,GAAG;AACf,aAAO,SAAS,OAAO;AAAA,IACzB;AACA,cAAU,OAAO;AAAA,EACnB;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,YAAY,KAAK;AAAA,IACjB,eAAe,OAAO;AAAA,IACtB,UAAU,sBAAc,WAAW;AAAA,IACnC,IAAI,sBAAc,EAAE;AAAA,IACpB,MAAM,sBAAc,WAAY;AAC9B,SAAG,EAAE;AAAA,IACP,CAAC;AAAA,IACD,SAAS,sBAAc,WAAY;AACjC,SAAG,CAAC;AAAA,IACN,CAAC;AAAA,IACD,OAAO,sBAAc,KAAK;AAAA,EAC5B;AACF;;;ACxHA,IAAO,mBAAS,SAAU,QAAQ,SAAS;AACzC,MAAI,KAAK,WAAW,CAAC,GACnB,UAAU,GAAG,SACb,UAAU,GAAG,SACb,WAAW,GAAG;AAChB,MAAI,KAAK,OAAO,WAAW,KAAK,GAAG,CAAC,GAClC,QAAQ,GAAG,CAAC,GACZ,KAAK,GAAG,CAAC,GACT,UAAU,GAAG,SACb,WAAW,GAAG;AAChB,2BAAiB,cAAc,WAAY;AACzC,gBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC1D,YAAQ;AACR,iBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,IAAI;AAAA,EACnE,GAAG;AAAA,IACD;AAAA,EACF,CAAC;AACD,2BAAiB,cAAc,WAAY;AACzC,gBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC1D,aAAS;AACT,iBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,KAAK;AAAA,EACpE,GAAG;AAAA,IACD;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;AC3BA,IAAAC,iBAA0C;;;ACD1C,IAAI,eAAe,SAAU,IAAI;AAC/B,MAAI,OAAO,YAAY,OAAO,SAAS,mBAAmB,OAAO,SAAS,MAAM;AAC9E,WAAO,KAAK,IAAI,OAAO,aAAa,SAAS,gBAAgB,WAAW,SAAS,KAAK,SAAS;AAAA,EACjG;AACA,SAAO,GAAG;AACZ;AACA,IAAI,kBAAkB,SAAU,IAAI;AAClC,SAAO,GAAG,gBAAgB,KAAK,IAAI,SAAS,gBAAgB,cAAc,SAAS,KAAK,YAAY;AACtG;AACA,IAAI,kBAAkB,SAAU,IAAI;AAClC,SAAO,GAAG,gBAAgB,KAAK,IAAI,SAAS,gBAAgB,cAAc,SAAS,KAAK,YAAY;AACtG;;;ADHA,IAAI,oBAAoB,SAAU,SAAS,SAAS;AAClD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,SAAS,QAAQ,QACnB,WAAW,QAAQ,UACnB,KAAK,QAAQ,WACb,YAAY,OAAO,SAAS,MAAM,IAClC,KAAK,QAAQ,WACb,YAAY,OAAO,SAAS,WAAW,IACvC,KAAK,QAAQ,YACb,aAAa,OAAO,SAAS,CAAC,IAAI,IAClC,SAAS,QAAQ,QACjB,WAAW,QAAQ,UACnB,YAAY,QAAQ,WACpB,UAAU,QAAQ,SAClB,YAAY,QAAQ;AACtB,MAAI,KAAK,WAAO,yBAAS,GAAG,CAAC,GAC3B,YAAY,GAAG,CAAC,GAChB,eAAe,GAAG,CAAC;AACrB,MAAI,KAAK,WAAO,yBAAS,KAAK,GAAG,CAAC,GAChC,cAAc,GAAG,CAAC,GAClB,iBAAiB,GAAG,CAAC;AACvB,MAAI,gBAAgB,cAAc;AAElC,MAAI,oBAAgB,uBAAO,MAAS;AAEpC,MAAI,mBAAe,uBAAO,CAAC;AAC3B,MAAI,aAAS,wBAAQ,WAAY;AAC/B,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,WAAO,SAAS,SAAS;AAAA,EAC3B,GAAG,CAAC,SAAS,CAAC;AACd,MAAI,KAAKC,oBAAW,SAAU,UAAU;AACpC,WAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACnD,UAAI;AACJ,UAAIC,KAAIC,KAAIC;AACZ,aAAO,YAAY,MAAM,SAAUC,KAAI;AACrC,gBAAQA,IAAG,OAAO;AAAA,UAChB,KAAK;AACH,mBAAO,CAAC,GAAa,QAAQ,QAAQ,CAAC;AAAA,UACxC,KAAK;AACH,0BAAcA,IAAG,KAAK;AACtB,gBAAI,CAAC,UAAU;AACb,2BAAa,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG;AAAA,gBAC/C,MAAM,cAAc,CAAC,GAAG,QAAQH,MAAK,YAAY,UAAU,QAAQA,QAAO,SAASA,MAAK,CAAC,CAAC,GAAG,KAAK;AAAA,cACpG,CAAC,CAAC;AAAA,YACJ,OAAO;AACL,2BAAa,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG;AAAA,gBAC/C,MAAM,gBAAgB,cAAc,cAAc,CAAC,GAAG,OAAO,YAAY,IAAI,GAAG,KAAK,GAAG,QAAQC,MAAK,SAAS,UAAU,QAAQA,QAAO,SAASA,MAAK,CAAC,CAAC,GAAG,KAAK,IAAI,cAAc,cAAc,CAAC,GAAG,QAAQC,MAAK,SAAS,UAAU,QAAQA,QAAO,SAASA,MAAK,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,YAAY,IAAI,GAAG,KAAK;AAAA,cAC9S,CAAC,CAAC;AAAA,YACJ;AACA,mBAAO,CAAC,GAAc,WAAW;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD;AAAA,IACA,WAAW,SAAU,GAAG,GAAG,GAAG;AAC5B,qBAAe,KAAK;AACpB,oBAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,GAAG,CAAC;AAAA,IACtE;AAAA,IACA,UAAU,WAAY;AACpB,aAAO,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAAA,IACtE;AAAA,IACA,WAAW,SAAU,GAAG;AACtB,iBAAW,WAAY;AACrB,YAAI,eAAe;AACjB,cAAI,KAAK,iBAAiB,MAAM;AAChC,eAAK,OAAO,WAAW,SAAS,kBAAkB;AAClD,cAAI,IAAI;AACN,gBAAI,eAAe,gBAAgB,EAAE;AACrC,eAAG,SAAS,GAAG,eAAe,aAAa,OAAO;AAAA,UACpD;AAAA,QACF,OAAO;AAEL,uBAAa;AAAA,QACf;AAAA,MACF,CAAC;AACD,oBAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,CAAC;AAAA,IACnE;AAAA,IACA,SAAS,SAAU,GAAG;AACpB,aAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,CAAC;AAAA,IACpE;AAAA,EACF,CAAC,GACD,UAAU,GAAG,SACb,QAAQ,GAAG,OACX,MAAM,GAAG,KACT,WAAW,GAAG,UACd,SAAS,GAAG;AACd,MAAI,WAAW,sBAAc,WAAY;AACvC,QAAI,QAAQ;AACV;AAAA,IACF;AACA,mBAAe,IAAI;AACnB,QAAI,SAAS;AAAA,EACf,CAAC;AACD,MAAI,gBAAgB,sBAAc,WAAY;AAC5C,QAAI,QAAQ;AACV,aAAO,QAAQ,OAAO;AAAA,IACxB;AACA,mBAAe,IAAI;AACnB,WAAO,SAAS,SAAS;AAAA,EAC3B,CAAC;AACD,MAAI,SAAS,WAAY;AACvB,mBAAe,KAAK;AACpB,WAAO,IAAI;AAAA,EACb;AACA,MAAI,cAAc,WAAY;AAC5B,mBAAe,KAAK;AACpB,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,eAAe,WAAY;AAC7B,QAAI,KAAK,iBAAiB,MAAM;AAChC,QAAI,CAAC,IAAI;AACP;AAAA,IACF;AACA,QAAI,WAAW,OAAO,WAAW,SAAS,kBAAkB;AAC5D,QAAI,YAAY,aAAa,QAAQ;AACrC,QAAI,eAAe,gBAAgB,QAAQ;AAC3C,QAAI,eAAe,gBAAgB,QAAQ;AAC3C,QAAI,eAAe;AACjB,UAAI,cAAc,YAAY,UAAa,cAAc,UAAU,aAAa,aAAa,WAAW;AACtG,iBAAS;AAAA,MACX;AACA,oBAAc,UAAU;AACxB,mBAAa,UAAU,eAAe;AAAA,IACxC,WAAW,eAAe,aAAa,eAAe,WAAW;AAC/D,eAAS;AAAA,IACX;AAAA,EACF;AACA,2BAAiB,UAAU,WAAY;AACrC,QAAI,WAAW,aAAa;AAC1B;AAAA,IACF;AACA,iBAAa;AAAA,EACf,GAAG;AAAA,IACD;AAAA,EACF,CAAC;AACD,0BAAgB,WAAY;AAC1B,QAAI;AAAA,EACN,GAAG,cAAc,CAAC,GAAG,OAAO,UAAU,GAAG,KAAK,CAAC;AAC/C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,CAAC,eAAe;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,sBAAc,MAAM;AAAA,IAC5B,aAAa,sBAAc,WAAW;AAAA,IACtC,QAAQ;AAAA,IACR;AAAA,EACF;AACF;AACA,IAAO,4BAAQ;;;AErKf,IAAAE,iBAA+C;AAG/C,IAAI,cAAc,SAAU,IAAI,OAAO,SAAS;AAC9C,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,gBAAgB,sBAAc,EAAE;AACpC,MAAI,eAAW,uBAAO,IAAI;AAC1B,MAAI,YAAQ,4BAAY,WAAY;AAClC,QAAI,SAAS,SAAS;AACpB,oBAAc,SAAS,OAAO;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,CAAC;AACL,gCAAU,WAAY;AACpB,QAAI,CAAC,SAAS,KAAK,KAAK,QAAQ,GAAG;AACjC;AAAA,IACF;AACA,QAAI,QAAQ,WAAW;AACrB,oBAAc;AAAA,IAChB;AACA,aAAS,UAAU,YAAY,eAAe,KAAK;AACnD,WAAO;AAAA,EACT,GAAG,CAAC,OAAO,QAAQ,SAAS,CAAC;AAC7B,SAAO;AACT;AACA,IAAO,sBAAQ;;;CClBd,WAAW;AACZ;AAGA,MAAI,OAAO,WAAW,UAAU;AAC9B;AAAA,EACF;AAIA,MAAI,0BAA0B,UAC1B,+BAA+B,UAC/B,uBAAuB,OAAO,0BAA0B,WAAW;AAIrE,QAAI,EAAE,oBAAoB,OAAO,0BAA0B,YAAY;AACrE,aAAO;AAAA,QAAe,OAAO,0BAA0B;AAAA,QACrD;AAAA,QAAkB;AAAA,UAClB,KAAK,WAAY;AACf,mBAAO,KAAK,oBAAoB;AAAA,UAClC;AAAA,QACF;AAAA,MAAC;AAAA,IACH;AACA;AAAA,EACF;AAOA,WAAS,gBAAgB,KAAK;AAC5B,QAAI;AACF,aAAO,IAAI,eAAe,IAAI,YAAY,gBAAgB;AAAA,IAC5D,SAAS,GAAG;AAEV,aAAO;AAAA,IACT;AAAA,EACF;AAKA,MAAIC,YAAY,SAAS,UAAU;AACjC,QAAI,MAAM;AACV,QAAI,QAAQ,gBAAgB,GAAG;AAC/B,WAAO,OAAO;AACZ,YAAM,MAAM;AACZ,cAAQ,gBAAgB,GAAG;AAAA,IAC7B;AACA,WAAO;AAAA,EACT,EAAG,OAAO,QAAQ;AAQlB,MAAI,WAAW,CAAC;AAOhB,MAAI,qBAAqB;AAMzB,MAAI,kBAAkB;AAStB,WAAS,0BAA0B,OAAO;AACxC,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS,MAAM;AACpB,SAAK,aAAa,cAAc,MAAM,UAAU;AAChD,SAAK,qBAAqB,cAAc,MAAM,kBAAkB;AAChE,SAAK,mBAAmB,cAAc,MAAM,oBAAoB,aAAa,CAAC;AAC9E,SAAK,iBAAiB,CAAC,CAAC,MAAM;AAG9B,QAAI,aAAa,KAAK;AACtB,QAAI,aAAa,WAAW,QAAQ,WAAW;AAC/C,QAAI,mBAAmB,KAAK;AAC5B,QAAI,mBAAmB,iBAAiB,QAAQ,iBAAiB;AAGjE,QAAI,YAAY;AAGd,WAAK,oBAAoB,QAAQ,mBAAmB,YAAY,QAAQ,CAAC,CAAC;AAAA,IAC5E,OAAO;AAEL,WAAK,oBAAoB,KAAK,iBAAiB,IAAI;AAAA,IACrD;AAAA,EACF;AAYA,WAASC,sBAAqB,UAAU,aAAa;AAEnD,QAAI,UAAU,eAAe,CAAC;AAE9B,QAAI,OAAO,YAAY,YAAY;AACjC,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AAEA,QACE,QAAQ,QACR,QAAQ,KAAK,YAAY,KACzB,QAAQ,KAAK,YAAY,GACzB;AACA,YAAM,IAAI,MAAM,oCAAoC;AAAA,IACtD;AAGA,SAAK,yBAAyBC;AAAA,MAC1B,KAAK,uBAAuB,KAAK,IAAI;AAAA,MAAG,KAAK;AAAA,IAAgB;AAGjE,SAAK,YAAY;AACjB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,iBAAiB,CAAC;AACvB,SAAK,oBAAoB,KAAK,iBAAiB,QAAQ,UAAU;AAGjE,SAAK,aAAa,KAAK,gBAAgB,QAAQ,SAAS;AACxD,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,aAAa,KAAK,kBAAkB,IAAI,SAAS,QAAQ;AAC5D,aAAO,OAAO,QAAQ,OAAO;AAAA,IAC/B,CAAC,EAAE,KAAK,GAAG;AAGX,SAAK,uBAAuB,CAAC;AAE7B,SAAK,0BAA0B,CAAC;AAAA,EAClC;AAOA,EAAAD,sBAAqB,UAAU,mBAAmB;AAQlD,EAAAA,sBAAqB,UAAU,gBAAgB;AAM/C,EAAAA,sBAAqB,UAAU,wBAAwB;AAYvD,EAAAA,sBAAqB,2BAA2B,WAAW;AACzD,QAAI,CAAC,oBAAoB;AAKvB,2BAAqB,SAAS,oBAAoB,kBAAkB;AAClE,YAAI,CAAC,sBAAsB,CAAC,kBAAkB;AAC5C,4BAAkB,aAAa;AAAA,QACjC,OAAO;AACL,4BAAkB,sBAAsB,oBAAoB,gBAAgB;AAAA,QAC9E;AACA,iBAAS,QAAQ,SAASE,WAAU;AAClC,UAAAA,UAAS,uBAAuB;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAMA,EAAAF,sBAAqB,2BAA2B,WAAW;AACzD,yBAAqB;AACrB,sBAAkB;AAAA,EACpB;AAQA,EAAAA,sBAAqB,UAAU,UAAU,SAAS,QAAQ;AACxD,QAAI,0BAA0B,KAAK,oBAAoB,KAAK,SAAS,MAAM;AACzE,aAAO,KAAK,WAAW;AAAA,IACzB,CAAC;AAED,QAAI,yBAAyB;AAC3B;AAAA,IACF;AAEA,QAAI,EAAE,UAAU,OAAO,YAAY,IAAI;AACrC,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAEA,SAAK,kBAAkB;AACvB,SAAK,oBAAoB,KAAK,EAAC,SAAS,QAAQ,OAAO,KAAI,CAAC;AAC5D,SAAK,sBAAsB,OAAO,aAAa;AAC/C,SAAK,uBAAuB;AAAA,EAC9B;AAOA,EAAAA,sBAAqB,UAAU,YAAY,SAAS,QAAQ;AAC1D,SAAK,sBACD,KAAK,oBAAoB,OAAO,SAAS,MAAM;AAC7C,aAAO,KAAK,WAAW;AAAA,IACzB,CAAC;AACL,SAAK,wBAAwB,OAAO,aAAa;AACjD,QAAI,KAAK,oBAAoB,UAAU,GAAG;AACxC,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAMA,EAAAA,sBAAqB,UAAU,aAAa,WAAW;AACrD,SAAK,sBAAsB,CAAC;AAC5B,SAAK,2BAA2B;AAChC,SAAK,oBAAoB;AAAA,EAC3B;AASA,EAAAA,sBAAqB,UAAU,cAAc,WAAW;AACtD,QAAI,UAAU,KAAK,eAAe,MAAM;AACxC,SAAK,iBAAiB,CAAC;AACvB,WAAO;AAAA,EACT;AAYA,EAAAA,sBAAqB,UAAU,kBAAkB,SAAS,eAAe;AACvE,QAAI,YAAY,iBAAiB,CAAC,CAAC;AACnC,QAAI,CAAC,MAAM,QAAQ,SAAS;AAAG,kBAAY,CAAC,SAAS;AAErD,WAAO,UAAU,KAAK,EAAE,OAAO,SAAS,GAAG,GAAG,GAAG;AAC/C,UAAI,OAAO,KAAK,YAAY,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AACtD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AACA,aAAO,MAAM,EAAE,IAAI,CAAC;AAAA,IACtB,CAAC;AAAA,EACH;AAcA,EAAAA,sBAAqB,UAAU,mBAAmB,SAAS,gBAAgB;AACzE,QAAI,eAAe,kBAAkB;AACrC,QAAI,UAAU,aAAa,MAAM,KAAK,EAAE,IAAI,SAAS,QAAQ;AAC3D,UAAI,QAAQ,wBAAwB,KAAK,MAAM;AAC/C,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AACA,aAAO,EAAC,OAAO,WAAW,MAAM,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,EAAC;AAAA,IACrD,CAAC;AAGD,YAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AACpC,YAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AACpC,YAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAEpC,WAAO;AAAA,EACT;AASA,EAAAA,sBAAqB,UAAU,wBAAwB,SAAS,KAAK;AACnE,QAAI,MAAM,IAAI;AACd,QAAI,CAAC,KAAK;AAER;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB,QAAQ,GAAG,KAAK,IAAI;AAEhD;AAAA,IACF;AAGA,QAAI,WAAW,KAAK;AACpB,QAAI,qBAAqB;AACzB,QAAI,cAAc;AAIlB,QAAI,KAAK,eAAe;AACtB,2BAAqB,IAAI,YAAY,UAAU,KAAK,aAAa;AAAA,IACnE,OAAO;AACL,eAAS,KAAK,UAAU,UAAU,IAAI;AACtC,eAAS,KAAK,UAAU,UAAU,IAAI;AACtC,UAAI,KAAK,yBAAyB,sBAAsB,KAAK;AAC3D,sBAAc,IAAI,IAAI,iBAAiB,QAAQ;AAC/C,oBAAY,QAAQ,KAAK;AAAA,UACvB,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAEA,SAAK,qBAAqB,KAAK,GAAG;AAClC,SAAK,wBAAwB,KAAK,WAAW;AAG3C,UAAIG,OAAM,IAAI;AAEd,UAAIA,MAAK;AACP,YAAI,oBAAoB;AACtB,UAAAA,KAAI,cAAc,kBAAkB;AAAA,QACtC;AACA,oBAAYA,MAAK,UAAU,UAAU,IAAI;AAAA,MAC3C;AAEA,kBAAY,KAAK,UAAU,UAAU,IAAI;AACzC,UAAI,aAAa;AACf,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF,CAAC;AAGD,QAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAUJ;AAC3D,QAAI,OAAO,SAAS;AAClB,UAAI,QAAQ,gBAAgB,GAAG;AAC/B,UAAI,OAAO;AACT,aAAK,sBAAsB,MAAM,aAAa;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAQA,EAAAC,sBAAqB,UAAU,0BAA0B,SAAS,KAAK;AACrE,QAAI,QAAQ,KAAK,qBAAqB,QAAQ,GAAG;AACjD,QAAI,SAAS,IAAI;AACf;AAAA,IACF;AAEA,QAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAUD;AAG3D,QAAI,sBACA,KAAK,oBAAoB,KAAK,SAAS,MAAM;AAC3C,UAAI,UAAU,KAAK,QAAQ;AAE3B,UAAI,WAAW,KAAK;AAClB,eAAO;AAAA,MACT;AAEA,aAAO,WAAW,WAAW,SAAS;AACpC,YAAIK,SAAQ,gBAAgB,OAAO;AACnC,kBAAUA,UAASA,OAAM;AACzB,YAAI,WAAW,KAAK;AAClB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AACL,QAAI,qBAAqB;AACvB;AAAA,IACF;AAGA,QAAI,cAAc,KAAK,wBAAwB,KAAK;AACpD,SAAK,qBAAqB,OAAO,OAAO,CAAC;AACzC,SAAK,wBAAwB,OAAO,OAAO,CAAC;AAC5C,gBAAY;AAGZ,QAAI,OAAO,SAAS;AAClB,UAAI,QAAQ,gBAAgB,GAAG;AAC/B,UAAI,OAAO;AACT,aAAK,wBAAwB,MAAM,aAAa;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAQA,EAAAJ,sBAAqB,UAAU,6BAA6B,WAAW;AACrE,QAAI,eAAe,KAAK,wBAAwB,MAAM,CAAC;AACvD,SAAK,qBAAqB,SAAS;AACnC,SAAK,wBAAwB,SAAS;AACtC,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,mBAAa,CAAC,EAAE;AAAA,IAClB;AAAA,EACF;AASA,EAAAA,sBAAqB,UAAU,yBAAyB,WAAW;AACjE,QAAI,CAAC,KAAK,QAAQ,sBAAsB,CAAC,iBAAiB;AAExD;AAAA,IACF;AAEA,QAAI,cAAc,KAAK,aAAa;AACpC,QAAI,WAAW,cAAc,KAAK,aAAa,IAAI,aAAa;AAEhE,SAAK,oBAAoB,QAAQ,SAAS,MAAM;AAC9C,UAAI,SAAS,KAAK;AAClB,UAAI,aAAa,sBAAsB,MAAM;AAC7C,UAAI,qBAAqB,KAAK,oBAAoB,MAAM;AACxD,UAAI,WAAW,KAAK;AACpB,UAAI,mBAAmB,eAAe,sBAClC,KAAK,kCAAkC,QAAQ,YAAY,QAAQ;AAEvE,UAAI,aAAa;AACjB,UAAI,CAAC,KAAK,oBAAoB,MAAM,GAAG;AACrC,qBAAa,aAAa;AAAA,MAC5B,WAAW,CAAC,sBAAsB,KAAK,MAAM;AAC3C,qBAAa;AAAA,MACf;AAEA,UAAI,WAAW,KAAK,QAAQ,IAAI,0BAA0B;AAAA,QACxD,MAAM,IAAI;AAAA,QACV;AAAA,QACA,oBAAoB;AAAA,QACpB;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAI,CAAC,UAAU;AACb,aAAK,eAAe,KAAK,QAAQ;AAAA,MACnC,WAAW,eAAe,oBAAoB;AAG5C,YAAI,KAAK,qBAAqB,UAAU,QAAQ,GAAG;AACjD,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF,OAAO;AAIL,YAAI,YAAY,SAAS,gBAAgB;AACvC,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,IACF,GAAG,IAAI;AAEP,QAAI,KAAK,eAAe,QAAQ;AAC9B,WAAK,UAAU,KAAK,YAAY,GAAG,IAAI;AAAA,IACzC;AAAA,EACF;AAgBA,EAAAA,sBAAqB,UAAU,oCAC3B,SAAS,QAAQ,YAAY,UAAU;AAEzC,QAAI,OAAO,iBAAiB,MAAM,EAAE,WAAW;AAAQ;AAEvD,QAAI,mBAAmB;AACvB,QAAI,SAAS,cAAc,MAAM;AACjC,QAAI,SAAS;AAEb,WAAO,CAAC,UAAU,QAAQ;AACxB,UAAI,aAAa;AACjB,UAAI,sBAAsB,OAAO,YAAY,IACzC,OAAO,iBAAiB,MAAM,IAAI,CAAC;AAGvC,UAAI,oBAAoB,WAAW;AAAQ,eAAO;AAElD,UAAI,UAAU,KAAK,QAAQ,OAAO;AAAA,MAA2B,GAAG;AAC9D,iBAAS;AACT,YAAI,UAAU,KAAK,QAAQ,UAAUD,WAAU;AAC7C,cAAI,sBAAsB,CAAC,KAAK,MAAM;AACpC,gBAAI,CAAC,mBACD,gBAAgB,SAAS,KAAK,gBAAgB,UAAU,GAAG;AAE7D,uBAAS;AACT,2BAAa;AACb,iCAAmB;AAAA,YACrB,OAAO;AACL,2BAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,yBAAa;AAAA,UACf;AAAA,QACF,OAAO;AAEL,cAAI,QAAQ,cAAc,MAAM;AAChC,cAAI,YAAY,SAAS,sBAAsB,KAAK;AACpD,cAAI,iBACA,SACA,KAAK,kCAAkC,OAAO,WAAW,QAAQ;AACrE,cAAI,aAAa,gBAAgB;AAC/B,qBAAS;AACT,yBAAa,sBAAsB,WAAW,cAAc;AAAA,UAC9D,OAAO;AACL,qBAAS;AACT,+BAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF,OAAO;AAKL,YAAI,MAAM,OAAO;AACjB,YAAI,UAAU,IAAI,QACd,UAAU,IAAI,mBACd,oBAAoB,YAAY,WAAW;AAC7C,uBAAa,sBAAsB,MAAM;AAAA,QAC3C;AAAA,MACF;AAIA,UAAI,YAAY;AACd,2BAAmB,wBAAwB,YAAY,gBAAgB;AAAA,MACzE;AACA,UAAI,CAAC;AAAkB;AACvB,eAAS,UAAU,cAAc,MAAM;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAQA,EAAAC,sBAAqB,UAAU,eAAe,WAAW;AACvD,QAAI;AACJ,QAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,GAAG;AAClC,iBAAW,sBAAsB,KAAK,IAAI;AAAA,IAC5C,OAAO;AAEL,UAAI,MAAM,MAAM,KAAK,IAAI,IAAI,KAAK,OAAOD;AACzC,UAAI,OAAO,IAAI;AACf,UAAI,OAAO,IAAI;AACf,iBAAW;AAAA,QACT,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO,KAAK,eAAe,KAAK;AAAA,QAChC,OAAO,KAAK,eAAe,KAAK;AAAA,QAChC,QAAQ,KAAK,gBAAgB,KAAK;AAAA,QAClC,QAAQ,KAAK,gBAAgB,KAAK;AAAA,MACpC;AAAA,IACF;AACA,WAAO,KAAK,wBAAwB,QAAQ;AAAA,EAC9C;AASA,EAAAC,sBAAqB,UAAU,0BAA0B,SAAS,MAAM;AACtE,QAAI,UAAU,KAAK,kBAAkB,IAAI,SAAS,QAAQ,GAAG;AAC3D,aAAO,OAAO,QAAQ,OAAO,OAAO,QAChC,OAAO,SAAS,IAAI,IAAI,KAAK,QAAQ,KAAK,UAAU;AAAA,IAC1D,CAAC;AACD,QAAI,UAAU;AAAA,MACZ,KAAK,KAAK,MAAM,QAAQ,CAAC;AAAA,MACzB,OAAO,KAAK,QAAQ,QAAQ,CAAC;AAAA,MAC7B,QAAQ,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC/B,MAAM,KAAK,OAAO,QAAQ,CAAC;AAAA,IAC7B;AACA,YAAQ,QAAQ,QAAQ,QAAQ,QAAQ;AACxC,YAAQ,SAAS,QAAQ,SAAS,QAAQ;AAE1C,WAAO;AAAA,EACT;AAaA,EAAAA,sBAAqB,UAAU,uBAC3B,SAAS,UAAU,UAAU;AAI/B,QAAI,WAAW,YAAY,SAAS,iBAChC,SAAS,qBAAqB,IAAI;AACtC,QAAI,WAAW,SAAS,iBACpB,SAAS,qBAAqB,IAAI;AAGtC,QAAI,aAAa;AAAU;AAE3B,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,UAAI,YAAY,KAAK,WAAW,CAAC;AAIjC,UAAI,aAAa,YAAY,aAAa,YACtC,YAAY,aAAa,YAAY,UAAU;AACjD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAQA,EAAAA,sBAAqB,UAAU,eAAe,WAAW;AACvD,WAAO,CAAC,KAAK,QAAQ,aAAaD,WAAU,KAAK,IAAI;AAAA,EACvD;AASA,EAAAC,sBAAqB,UAAU,sBAAsB,SAAS,QAAQ;AACpE,QAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAUD;AAC3D,WACE,aAAa,SAAS,MAAM,MAC3B,CAAC,KAAK,QAAQ,WAAW,OAAO;AAAA,EAErC;AAQA,EAAAC,sBAAqB,UAAU,oBAAoB,WAAW;AAC5D,QAAI,SAAS,QAAQ,IAAI,IAAI,GAAG;AAC9B,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AAOA,EAAAA,sBAAqB,UAAU,sBAAsB,WAAW;AAC9D,QAAI,QAAQ,SAAS,QAAQ,IAAI;AACjC,QAAI,SAAS;AAAI,eAAS,OAAO,OAAO,CAAC;AAAA,EAC3C;AAQA,WAAS,MAAM;AACb,WAAO,OAAO,eAAe,YAAY,OAAO,YAAY,IAAI;AAAA,EAClE;AAWA,WAASC,UAAS,IAAI,SAAS;AAC7B,QAAI,QAAQ;AACZ,WAAO,WAAY;AACjB,UAAI,CAAC,OAAO;AACV,gBAAQ,WAAW,WAAW;AAC5B,aAAG;AACH,kBAAQ;AAAA,QACV,GAAG,OAAO;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAWA,WAAS,SAAS,MAAM,OAAO,IAAI,gBAAgB;AACjD,QAAI,OAAO,KAAK,oBAAoB,YAAY;AAC9C,WAAK,iBAAiB,OAAO,IAAI,kBAAkB,KAAK;AAAA,IAC1D,WACS,OAAO,KAAK,eAAe,YAAY;AAC9C,WAAK,YAAY,OAAO,OAAO,EAAE;AAAA,IACnC;AAAA,EACF;AAWA,WAAS,YAAY,MAAM,OAAO,IAAI,gBAAgB;AACpD,QAAI,OAAO,KAAK,uBAAuB,YAAY;AACjD,WAAK,oBAAoB,OAAO,IAAI,kBAAkB,KAAK;AAAA,IAC7D,WACS,OAAO,KAAK,eAAe,YAAY;AAC9C,WAAK,YAAY,OAAO,OAAO,EAAE;AAAA,IACnC;AAAA,EACF;AAUA,WAAS,wBAAwB,OAAO,OAAO;AAC7C,QAAI,MAAM,KAAK,IAAI,MAAM,KAAK,MAAM,GAAG;AACvC,QAAI,SAAS,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AAChD,QAAI,OAAO,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI;AAC1C,QAAI,QAAQ,KAAK,IAAI,MAAM,OAAO,MAAM,KAAK;AAC7C,QAAI,QAAQ,QAAQ;AACpB,QAAI,SAAS,SAAS;AAEtB,WAAQ,SAAS,KAAK,UAAU,KAAM;AAAA,MACpC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,KAAK;AAAA,EACP;AAQA,WAAS,sBAAsB,IAAI;AACjC,QAAI;AAEJ,QAAI;AACF,aAAO,GAAG,sBAAsB;AAAA,IAClC,SAAS,KAAK;AAAA,IAGd;AAEA,QAAI,CAAC;AAAM,aAAO,aAAa;AAG/B,QAAI,EAAE,KAAK,SAAS,KAAK,SAAS;AAChC,aAAO;AAAA,QACL,KAAK,KAAK;AAAA,QACV,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK;AAAA,QACX,OAAO,KAAK,QAAQ,KAAK;AAAA,QACzB,QAAQ,KAAK,SAAS,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAQA,WAAS,eAAe;AACtB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AAUA,WAAS,cAAc,MAAM;AAE3B,QAAI,CAAC,QAAQ,OAAO,MAAM;AACxB,aAAO;AAAA,IACT;AAKA,WAAO;AAAA,MACL,KAAK,KAAK;AAAA,MACV,GAAG,KAAK;AAAA,MACR,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,MACX,GAAG,KAAK;AAAA,MACR,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AAUA,WAAS,sBAAsB,oBAAoB,wBAAwB;AACzE,QAAI,MAAM,uBAAuB,MAAM,mBAAmB;AAC1D,QAAI,OAAO,uBAAuB,OAAO,mBAAmB;AAC5D,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,QAAQ,uBAAuB;AAAA,MAC/B,OAAO,uBAAuB;AAAA,MAC9B,QAAQ,MAAM,uBAAuB;AAAA,MACrC,OAAO,OAAO,uBAAuB;AAAA,IACvC;AAAA,EACF;AAUA,WAAS,aAAa,QAAQ,OAAO;AACnC,QAAI,OAAO;AACX,WAAO,MAAM;AACX,UAAI,QAAQ;AAAQ,eAAO;AAE3B,aAAO,cAAc,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AASA,WAAS,cAAc,MAAM;AAC3B,QAAI,SAAS,KAAK;AAElB,QAAI,KAAK;AAAA,IAA2B,KAAK,QAAQF,WAAU;AAEzD,aAAO,gBAAgB,IAAI;AAAA,IAC7B;AAGA,QAAI,UAAU,OAAO,cAAc;AACjC,eAAS,OAAO,aAAa;AAAA,IAC/B;AAEA,QAAI,UAAU,OAAO,YAAY,MAAM,OAAO,MAAM;AAElD,aAAO,OAAO;AAAA,IAChB;AAEA,WAAO;AAAA,EACT;AAOA,WAAS,MAAM,MAAM;AACnB,WAAO,QAAQ,KAAK,aAAa;AAAA,EACnC;AAIA,SAAO,uBAAuBC;AAC9B,SAAO,4BAA4B;AAEnC,GAAE;;;ACn/BF,IAAAK,iBAAyB;AAGzB,SAAS,cAAc,QAAQ,SAAS;AACtC,MAAI,KAAK,WAAW,CAAC,GACnB,WAAW,GAAG,UACd,SAAS,OAAO,IAAI,CAAC,UAAU,CAAC;AAClC,MAAI,KAAK,WAAO,yBAAS,GAAG,CAAC,GAC3B,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,KAAK,WAAO,yBAAS,GAAG,CAAC,GAC3B,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,8BAAoB,WAAY;AAC9B,QAAI,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACtD,QAAI,MAAM,QAAQ,IAAI,SAAU,SAAS;AACvC,aAAO,iBAAiB,OAAO;AAAA,IACjC,CAAC,EAAE,OAAO,OAAO;AACjB,QAAI,CAAC,IAAI,QAAQ;AACf;AAAA,IACF;AACA,QAAIC,YAAW,IAAI,qBAAqB,SAAU,SAAS;AACzD,UAAI,KAAKC;AACT,UAAI;AACF,iBAAS,YAAY,SAAS,OAAO,GAAG,cAAc,UAAU,KAAK,GAAG,CAAC,YAAY,MAAM,cAAc,UAAU,KAAK,GAAG;AACzH,cAAI,QAAQ,YAAY;AACxB,mBAAS,MAAM,iBAAiB;AAChC,mBAAS,MAAM,cAAc;AAC7B,uBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,KAAK;AAAA,QACpE;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,eAAe,CAAC,YAAY,SAASA,MAAK,UAAU;AAAS,YAAAA,IAAG,KAAK,SAAS;AAAA,QACpF,UAAE;AACA,cAAI;AAAK,kBAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF,GAAG,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG;AAAA,MAChC,MAAM,iBAAiB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAI;AAAA,IACvF,CAAC,CAAC;AACF,QAAI,QAAQ,SAAU,IAAI;AACxB,aAAOD,UAAS,QAAQ,EAAE;AAAA,IAC5B,CAAC;AACD,WAAO,WAAY;AACjB,MAAAA,UAAS,WAAW;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,QAAQ,GAAG,MAAM;AAChK,SAAO,CAAC,OAAO,KAAK;AACtB;AACA,IAAO,wBAAQ;;;ACvDf,IAAAE,iBAA2C;AAE3C,IAAI,4BAA4B,oBAAY,iCAAkB;AAC9D,IAAO,oCAAQ;;;ACHf,IAAAC,iBAAuB;AAGvB,IAAI,iCAAiC,SAAU,QAAQ,MAAM,QAAQ;AACnE,MAAI,UAAM,uBAAO,MAAS;AAC1B,MAAI,gBAAY,uBAAO,CAAC;AACxB,MAAI,CAAC,UAAU,MAAM,IAAI,OAAO,GAAG;AACjC,cAAU,WAAW;AAAA,EACvB;AACA,MAAI,UAAU;AACd,8BAAoB,QAAQ,CAAC,UAAU,OAAO,GAAG,MAAM;AACzD;AACA,IAAO,mCAAQ;;;ACZf,IAAI,gBAAgB,0BAA0B,KAAK,OAAO,cAAc,cAAc,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,WAAW,EAAE;AACnK,IAAO,wBAAQ;;;ACMf,IAAI,kBAAkB;AAAA,EACpB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,WAAW;AAAA,EACX,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,KAAK;AAAA,EACL,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,MAAM,wBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;AAAA,EACxC,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,KAAK;AAAA,EACL,UAAU;AAAA,EACV,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,cAAc;AAAA,EACd,aAAa;AACf;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM,SAAU,OAAO;AACrB,WAAO,MAAM;AAAA,EACf;AAAA,EACA,OAAO,SAAU,OAAO;AACtB,WAAO,MAAM;AAAA,EACf;AAAA,EACA,KAAK,SAAU,OAAO;AACpB,WAAO,MAAM;AAAA,EACf;AAAA,EACA,MAAM,SAAU,OAAO;AACrB,QAAI,MAAM,SAAS,SAAS;AAC1B,aAAO,gBAAgB,KAAK,SAAS,MAAM,OAAO;AAAA,IACpD;AACA,WAAO,MAAM;AAAA,EACf;AACF;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,SAAS,KAAK,KAAK,SAAS,KAAK;AAC1C;AAEA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,kBAAkB,OAAO,KAAK,WAAW,EAAE,OAAO,SAAU,OAAO,KAAK;AAC1E,QAAI,YAAY,GAAG,EAAE,KAAK,GAAG;AAC3B,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO;AAAA,EACT,GAAG,CAAC;AAEJ,SAAO,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE,SAAS,MAAM,OAAO,IAAI,kBAAkB,kBAAkB;AAC5F;AAOA,SAAS,aAAa,OAAO,WAAW,YAAY;AAClD,MAAI,KAAK;AAET,MAAI,CAAC,MAAM,KAAK;AACd,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,SAAS,GAAG;AACvB,WAAO,MAAM,YAAY,YAAY,YAAY;AAAA,EACnD;AAEA,MAAI,SAAS,UAAU,MAAM,GAAG;AAChC,MAAI,SAAS;AACb,MAAI;AACF,aAAS,WAAW,SAAS,MAAM,GAAG,aAAa,SAAS,KAAK,GAAG,CAAC,WAAW,MAAM,aAAa,SAAS,KAAK,GAAG;AAClH,UAAI,MAAM,WAAW;AAErB,UAAI,cAAc,YAAY,GAAG;AAEjC,UAAI,eAAe,gBAAgB,IAAI,YAAY,CAAC;AACpD,UAAI,eAAe,YAAY,KAAK,KAAK,gBAAgB,iBAAiB,MAAM,SAAS;AACvF;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,UAAM;AAAA,MACJ,OAAO;AAAA,IACT;AAAA,EACF,UAAE;AACA,QAAI;AACF,UAAI,cAAc,CAAC,WAAW,SAAS,KAAK,SAAS;AAAS,WAAG,KAAK,QAAQ;AAAA,IAChF,UAAE;AACA,UAAI;AAAK,cAAM,IAAI;AAAA,IACrB;AAAA,EACF;AAOA,MAAI,YAAY;AACd,WAAO,WAAW,OAAO,UAAU,gBAAgB,KAAK,MAAM,OAAO,SAAS,YAAY;AAAA,EAC5F;AACA,SAAO,WAAW,OAAO,SAAS,YAAY;AAChD;AAMA,SAAS,gBAAgB,WAAW,YAAY;AAC9C,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,eAAe,SAAS,GAAG;AAC7B,WAAO,SAAU,OAAO;AACtB,aAAO,aAAa,OAAO,WAAW,UAAU;AAAA,IAClD;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,WAAO,SAAU,OAAO;AACtB,aAAO,UAAU,KAAK,SAAU,MAAM;AACpC,eAAO,aAAa,OAAO,MAAM,UAAU;AAAA,MAC7C,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,WAAY;AACjB,WAAO,QAAQ,SAAS;AAAA,EAC1B;AACF;AACA,IAAI,gBAAgB,CAAC,SAAS;AAC9B,SAAS,YAAY,WAAW,cAAc,QAAQ;AACpD,MAAI,KAAK,UAAU,CAAC,GAClB,KAAK,GAAG,QACR,SAAS,OAAO,SAAS,gBAAgB,IACzC,SAAS,GAAG,QACZ,KAAK,GAAG,YACR,aAAa,OAAO,SAAS,QAAQ,IACrC,KAAK,GAAG,YACR,aAAa,OAAO,SAAS,QAAQ;AACvC,MAAI,kBAAkB,kBAAU,YAAY;AAC5C,MAAI,eAAe,kBAAU,SAAS;AACtC,mCAA+B,WAAY;AACzC,QAAI,KAAKC;AACT,QAAIC;AACJ,QAAI,KAAK,iBAAiB,QAAQ,MAAM;AACxC,QAAI,CAAC,IAAI;AACP;AAAA,IACF;AACA,QAAI,kBAAkB,SAAU,OAAO;AACrC,UAAID;AACJ,UAAI,WAAW,gBAAgB,aAAa,SAAS,UAAU;AAC/D,UAAI,WAAW,SAAS,KAAK;AAC7B,UAAI,WAAW,eAAe,QAAQ,IAAI,WAAW,MAAM;AAC3D,UAAI,UAAU;AACZ,gBAAQA,MAAK,gBAAgB,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,iBAAiB,OAAO,QAAQ;AAAA,MACrH;AAAA,IACF;AACA,QAAI;AACF,eAAS,WAAW,SAAS,MAAM,GAAG,aAAa,SAAS,KAAK,GAAG,CAAC,WAAW,MAAM,aAAa,SAAS,KAAK,GAAG;AAClH,YAAI,YAAY,WAAW;AAC3B,SAACC,MAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,sBAAsB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,IAAI,WAAW,iBAAiB,UAAU;AAAA,MAC5J;AAAA,IACF,SAAS,OAAO;AACd,YAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,UAAE;AACA,UAAI;AACF,YAAI,cAAc,CAAC,WAAW,SAASD,MAAK,SAAS;AAAS,UAAAA,IAAG,KAAK,QAAQ;AAAA,MAChF,UAAE;AACA,YAAI;AAAK,gBAAM,IAAI;AAAA,MACrB;AAAA,IACF;AACA,WAAO,WAAY;AACjB,UAAI,KAAKA;AACT,UAAIC;AACJ,UAAI;AACF,iBAAS,WAAW,SAAS,MAAM,GAAG,aAAa,SAAS,KAAK,GAAG,CAAC,WAAW,MAAM,aAAa,SAAS,KAAK,GAAG;AAClH,cAAIC,aAAY,WAAW;AAC3B,WAACD,MAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,yBAAyB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,IAAIC,YAAW,iBAAiB,UAAU;AAAA,QAC/J;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,cAAc,CAAC,WAAW,SAASF,MAAK,SAAS;AAAS,YAAAA,IAAG,KAAK,QAAQ;AAAA,QAChF,UAAE;AACA,cAAI;AAAK,kBAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,MAAM,GAAG,MAAM;AACrB;AACA,IAAO,sBAAQ;;;AC5Rf,IAAAG,iBAAyB;AAKlB,IAAI,0BAA0B;AAC9B,SAAS,sBAAsB,YAAY;AAChD,WAAS,gBAAgB,KAAK,SAAS;AACrC,QAAI,YAAY,QAAQ;AACtB,gBAAU,CAAC;AAAA,IACb;AACA,QAAI;AACJ,QAAI,KAAK,QAAQ,qBACf,sBAAsB,OAAO,SAAS,QAAQ,IAC9C,KAAK,QAAQ,SACb,UAAU,OAAO,SAAS,SAAU,GAAG;AACrC,cAAQ,MAAM,CAAC;AAAA,IACjB,IAAI;AAEN,QAAI;AACF,gBAAU,WAAW;AAAA,IACvB,SAAS,KAAK;AACZ,cAAQ,GAAG;AAAA,IACb;AACA,QAAI,aAAa,SAAU,OAAO;AAChC,UAAI,QAAQ,YAAY;AACtB,eAAO,QAAQ,WAAW,KAAK;AAAA,MACjC;AACA,aAAO,KAAK,UAAU,KAAK;AAAA,IAC7B;AACA,QAAI,eAAe,SAAU,OAAO;AAClC,UAAI,QAAQ,cAAc;AACxB,eAAO,QAAQ,aAAa,KAAK;AAAA,MACnC;AACA,aAAO,KAAK,MAAM,KAAK;AAAA,IACzB;AACA,aAAS,iBAAiB;AACxB,UAAI;AACF,YAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ,GAAG;AAC/E,YAAI,KAAK;AACP,iBAAO,aAAa,GAAG;AAAA,QACzB;AAAA,MACF,SAAS,GAAG;AACV,gBAAQ,CAAC;AAAA,MACX;AACA,UAAI,WAAW,QAAQ,YAAY,GAAG;AACpC,eAAO,QAAQ,aAAa;AAAA,MAC9B;AACA,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,KAAK,WAAO,yBAAS,cAAc,GAAG,CAAC,GACzC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,4BAAgB,WAAY;AAC1B,eAAS,eAAe,CAAC;AAAA,IAC3B,GAAG,CAAC,GAAG,CAAC;AACR,QAAI,cAAc,SAAU,OAAO;AACjC,UAAI,eAAe,WAAW,KAAK,IAAI,MAAM,KAAK,IAAI;AACtD,UAAI,CAAC,qBAAqB;AACxB,iBAAS,YAAY;AAAA,MACvB;AACA,UAAI;AACF,YAAI,WAAW;AACf,YAAI,WAAW,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ,GAAG;AACpF,YAAI,QAAQ,YAAY,GAAG;AACzB,qBAAW;AACX,sBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,GAAG;AAAA,QAC1E,OAAO;AACL,qBAAW,WAAW,YAAY;AAClC,sBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ,KAAK,QAAQ;AAAA,QACjF;AACA;AAAA;AAAA;AAAA;AAAA,UAIA,IAAI,YAAY,yBAAyB;AAAA,YACvC,QAAQ;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QAAC;AAAA,MACJ,SAAS,GAAG;AACV,gBAAQ,CAAC;AAAA,MACX;AAAA,IACF;AACA,QAAI,YAAY,SAAU,OAAO;AAC/B,UAAI,MAAM,QAAQ,OAAO,MAAM,gBAAgB,SAAS;AACtD;AAAA,MACF;AACA,eAAS,eAAe,CAAC;AAAA,IAC3B;AACA,QAAI,2BAA2B,SAAU,OAAO;AAC9C,gBAAU,MAAM,MAAM;AAAA,IACxB;AAEA,6BAAiB,WAAW,WAAW;AAAA,MACrC,QAAQ;AAAA,IACV,CAAC;AAED,6BAAiB,yBAAyB,0BAA0B;AAAA,MAClE,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,CAAC,OAAO,sBAAc,WAAW,CAAC;AAAA,EAC3C;AACA,SAAO;AACT;;;AC1GA,IAAI,uBAAuB,sBAAsB,WAAY;AAC3D,SAAO,oBAAY,eAAe;AACpC,CAAC;AACD,IAAO,+BAAQ;;;ACJf,IAAAC,iBAAoC;AACpC,SAAS,UAAU,IAAI;AACrB,MAAI,QAAQ;AACZ,MAAI,cAAU,uBAAO,KAAK;AAC1B,aAAO,4BAAY,WAAY;AAC7B,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,WAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAClD,UAAI,KAAK;AACT,aAAO,YAAY,MAAM,SAAU,IAAI;AACrC,gBAAQ,GAAG,OAAO;AAAA,UAChB,KAAK;AACH,gBAAI,QAAQ,SAAS;AACnB,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,YACtB;AACA,oBAAQ,UAAU;AAClB,eAAG,QAAQ;AAAA,UACb,KAAK;AACH,eAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,mBAAO,CAAC,GAAa,GAAG,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,UAC/E,KAAK;AACH,kBAAM,GAAG,KAAK;AACd,mBAAO,CAAC,GAAc,GAAG;AAAA,UAC3B,KAAK;AACH,kBAAM,GAAG,KAAK;AACd,kBAAM;AAAA,UACR,KAAK;AACH,oBAAQ,UAAU;AAClB,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAgB;AAAA,UAC1B,KAAK;AACH,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,EAAE,CAAC;AACT;AACA,IAAO,oBAAQ;;;ACvCf,IAAAC,iBAAuB;AAIvB,SAAS,aAAa,aAAa,QAAQ,IAAI;AAC7C,MAAI,KAAK,OAAO,SAAS,CAAC,IAAI,IAC5B,KAAK,GAAG,OACR,QAAQ,OAAO,SAAS,MAAM,IAC9B,gBAAgB,GAAG,eACnB,UAAU,GAAG,SACb,iBAAiB,GAAG;AACtB,MAAI,iBAAiB,kBAAU,WAAW;AAC1C,MAAI,aAAa,kBAAU,OAAO;AAClC,MAAI,oBAAoB,kBAAU,cAAc;AAChD,MAAI,eAAW,uBAAO,MAAS;AAC/B,MAAI,qBAAiB,uBAAO,KAAK;AACjC,MAAI,sBAAkB,uBAAO;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACD,MAAI,mBAAe,uBAAO,KAAK;AAC/B,MAAI,mBAAe,uBAAO,KAAK;AAC/B,MAAI,mBAAmB,CAAC,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM,cAAc,IAAI,MAAM,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM,cAAc,IAAI;AACzO,8BAAoB,WAAY;AAC9B,QAAI,gBAAgB,iBAAiB,MAAM;AAC3C,QAAI,EAAE,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,mBAAmB;AACnG;AAAA,IACF;AACA,QAAI,gBAAgB,SAAU,OAAO;AACnC,UAAIC,MAAK,kBAAkB,KAAK,GAC9B,UAAUA,IAAG,SACb,UAAUA,IAAG;AACf,UAAI,UAAU,KAAK,IAAI,UAAU,gBAAgB,QAAQ,CAAC;AAC1D,UAAI,UAAU,KAAK,IAAI,UAAU,gBAAgB,QAAQ,CAAC;AAC1D,aAAO,CAAC,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM,UAAU,cAAc,MAAM,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM,UAAU,cAAc;AAAA,IACvO;AACA,aAAS,kBAAkB,OAAO;AAChC,UAAI,gBAAgB,UAAU,iBAAiB,YAAY;AACzD,eAAO;AAAA,UACL,SAAS,MAAM,QAAQ,CAAC,EAAE;AAAA,UAC1B,SAAS,MAAM,QAAQ,CAAC,EAAE;AAAA,QAC5B;AAAA,MACF;AACA,UAAI,iBAAiB,YAAY;AAC/B,eAAO;AAAA,UACL,SAAS,MAAM;AAAA,UACf,SAAS,MAAM;AAAA,QACjB;AAAA,MACF;AACA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,cAAc,SAAU,OAAO;AACjC,eAAS,UAAU,WAAW,WAAY;AACxC,uBAAe,QAAQ,KAAK;AAC5B,uBAAe,UAAU;AAAA,MAC3B,GAAG,KAAK;AAAA,IACV;AACA,QAAI,eAAe,SAAU,OAAO;AAClC,UAAI,aAAa,SAAS;AACxB;AAAA,MACF;AACA,mBAAa,UAAU;AACvB,UAAI,kBAAkB;AACpB,YAAIA,MAAK,kBAAkB,KAAK,GAC9B,UAAUA,IAAG,SACb,UAAUA,IAAG;AACf,wBAAgB,QAAQ,IAAI;AAC5B,wBAAgB,QAAQ,IAAI;AAAA,MAC9B;AACA,kBAAY,KAAK;AAAA,IACnB;AACA,QAAI,cAAc,SAAU,OAAO;AACjC,UAAIA;AACJ,WAAKA,MAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,wBAAwB,QAAQA,QAAO,SAAS,SAASA,IAAG,kBAAkB;AAC1I;AAAA,MACF;AACA,mBAAa,UAAU;AACvB,UAAI,kBAAkB;AACpB,wBAAgB,QAAQ,IAAI,MAAM;AAClC,wBAAgB,QAAQ,IAAI,MAAM;AAAA,MACpC;AACA,kBAAY,KAAK;AAAA,IACnB;AACA,QAAI,SAAS,SAAU,OAAO;AAC5B,UAAI,SAAS,WAAW,cAAc,KAAK,GAAG;AAC5C,qBAAa,SAAS,OAAO;AAC7B,iBAAS,UAAU;AAAA,MACrB;AAAA,IACF;AACA,QAAI,aAAa,SAAU,OAAO;AAChC,UAAIA;AACJ,UAAI,CAAC,aAAa,SAAS;AACzB;AAAA,MACF;AACA,mBAAa,UAAU;AACvB,UAAI,SAAS,SAAS;AACpB,qBAAa,SAAS,OAAO;AAC7B,iBAAS,UAAU;AAAA,MACrB;AACA,UAAI,eAAe,SAAS;AAC1B,SAACA,MAAK,kBAAkB,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,mBAAmB,KAAK;AAAA,MACxG,WAAW,WAAW,SAAS;AAC7B,mBAAW,QAAQ,KAAK;AAAA,MAC1B;AACA,qBAAe,UAAU;AAAA,IAC3B;AACA,QAAI,YAAY,SAAU,OAAO;AAC/B,UAAIA,KAAIC;AACR,WAAKD,MAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,wBAAwB,QAAQA,QAAO,SAAS,SAASA,IAAG,kBAAkB;AAC1I;AAAA,MACF;AACA,UAAI,CAAC,aAAa,SAAS;AACzB;AAAA,MACF;AACA,mBAAa,UAAU;AACvB,UAAI,SAAS,SAAS;AACpB,qBAAa,SAAS,OAAO;AAC7B,iBAAS,UAAU;AAAA,MACrB;AACA,UAAI,eAAe,SAAS;AAC1B,SAACC,MAAK,kBAAkB,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,mBAAmB,KAAK;AAAA,MACxG,WAAW,WAAW,SAAS;AAC7B,mBAAW,QAAQ,KAAK;AAAA,MAC1B;AACA,qBAAe,UAAU;AAAA,IAC3B;AACA,QAAI,eAAe,SAAU,OAAO;AAClC,UAAID;AACJ,UAAI,CAAC,aAAa,SAAS;AACzB;AAAA,MACF;AACA,mBAAa,UAAU;AACvB,UAAI,SAAS,SAAS;AACpB,qBAAa,SAAS,OAAO;AAC7B,iBAAS,UAAU;AAAA,MACrB;AACA,UAAI,eAAe,SAAS;AAC1B,SAACA,MAAK,kBAAkB,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,mBAAmB,KAAK;AACtG,uBAAe,UAAU;AAAA,MAC3B;AAAA,IACF;AACA,kBAAc,iBAAiB,aAAa,WAAW;AACvD,kBAAc,iBAAiB,WAAW,SAAS;AACnD,kBAAc,iBAAiB,cAAc,YAAY;AACzD,kBAAc,iBAAiB,cAAc,YAAY;AACzD,kBAAc,iBAAiB,YAAY,UAAU;AACrD,QAAI,kBAAkB;AACpB,oBAAc,iBAAiB,aAAa,MAAM;AAClD,oBAAc,iBAAiB,aAAa,MAAM;AAAA,IACpD;AACA,WAAO,WAAY;AACjB,UAAI,SAAS,SAAS;AACpB,qBAAa,SAAS,OAAO;AAC7B,uBAAe,UAAU;AAAA,MAC3B;AACA,oBAAc,oBAAoB,aAAa,WAAW;AAC1D,oBAAc,oBAAoB,WAAW,SAAS;AACtD,oBAAc,oBAAoB,cAAc,YAAY;AAC5D,oBAAc,oBAAoB,cAAc,YAAY;AAC5D,oBAAc,oBAAoB,YAAY,UAAU;AACxD,UAAI,kBAAkB;AACpB,sBAAc,oBAAoB,aAAa,MAAM;AACrD,sBAAc,oBAAoB,aAAa,MAAM;AAAA,MACvD;AAAA,IACF;AAAA,EACF,GAAG,CAAC,GAAG,MAAM;AACf;AACA,IAAO,uBAAQ;;;ACzKf,IAAAE,iBAAyB;AAEzB,SAAS,OAAO,cAAc;AAC5B,MAAI,eAAe,WAAY;AAC7B,WAAO,IAAI,IAAI,YAAY;AAAA,EAC7B;AACA,MAAI,KAAK,WAAO,yBAAS,YAAY,GAAG,CAAC,GACvC,MAAM,GAAG,CAAC,GACV,SAAS,GAAG,CAAC;AACf,MAAI,MAAM,SAAU,KAAK,OAAO;AAC9B,WAAO,SAAU,MAAM;AACrB,UAAI,OAAO,IAAI,IAAI,IAAI;AACvB,WAAK,IAAI,KAAK,KAAK;AACnB,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,SAAS,SAAU,QAAQ;AAC7B,WAAO,IAAI,IAAI,MAAM,CAAC;AAAA,EACxB;AACA,MAAI,SAAS,SAAU,KAAK;AAC1B,WAAO,SAAU,MAAM;AACrB,UAAI,OAAO,IAAI,IAAI,IAAI;AACvB,WAAK,OAAO,GAAG;AACf,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,WAAY;AACtB,WAAO,OAAO,aAAa,CAAC;AAAA,EAC9B;AACA,MAAI,MAAM,SAAU,KAAK;AACvB,WAAO,IAAI,IAAI,GAAG;AAAA,EACpB;AACA,SAAO,CAAC,KAAK;AAAA,IACX,KAAK,sBAAc,GAAG;AAAA,IACtB,QAAQ,sBAAc,MAAM;AAAA,IAC5B,QAAQ,sBAAc,MAAM;AAAA,IAC5B,OAAO,sBAAc,KAAK;AAAA,IAC1B,KAAK,sBAAc,GAAG;AAAA,EACxB,CAAC;AACH;AACA,IAAO,iBAAQ;;;ACxCf,IAAAC,iBAA8C;AAE9C,SAAS,YAAY,cAAc;AACjC,MAAI,UAAM,uBAAO,CAAC;AAClB,MAAI,KAAK,WAAO,yBAAS,YAAY,GAAG,CAAC,GACvC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,kBAAc,4BAAY,SAAU,OAAO;AAC7C,yBAAqB,IAAI,OAAO;AAChC,QAAI,UAAU,sBAAsB,WAAY;AAC9C,eAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,qBAAW,WAAY;AACrB,yBAAqB,IAAI,OAAO;AAAA,EAClC,CAAC;AACD,SAAO,CAAC,OAAO,WAAW;AAC5B;AACA,IAAO,sBAAQ;;;ACff,IAAI,YAAY;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AACf;AACA,IAAO,mBAAS,SAAU,QAAQ;AAChC,MAAI,KAAK,OAAO,oBAAY,SAAS,GAAG,CAAC,GACvC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,2BAAiB,aAAa,SAAU,OAAO;AAC7C,QAAI,UAAU,MAAM,SAClB,UAAU,MAAM,SAChB,UAAU,MAAM,SAChB,UAAU,MAAM,SAChB,QAAQ,MAAM,OACd,QAAQ,MAAM;AAChB,QAAI,WAAW;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AACA,QAAI,gBAAgB,iBAAiB,MAAM;AAC3C,QAAI,eAAe;AACjB,UAAIC,MAAK,cAAc,sBAAsB,GAC3C,OAAOA,IAAG,MACV,QAAQA,IAAG,KACX,QAAQA,IAAG,OACX,SAASA,IAAG;AACd,eAAS,cAAc,OAAO,OAAO;AACrC,eAAS,cAAc,QAAQ,OAAO;AACtC,eAAS,WAAW,QAAQ,SAAS;AACrC,eAAS,WAAW,QAAQ,SAAS;AACrC,eAAS,WAAW;AACpB,eAAS,WAAW;AAAA,IACtB;AACA,aAAS,QAAQ;AAAA,EACnB,GAAG;AAAA,IACD,QAAQ,WAAY;AAClB,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;AC/DA,IAAAC,iBAAoC;AAEpC,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAC3B,EAAAA,kBAAiB,QAAQ,IAAI;AAC7B,EAAAA,kBAAiB,SAAS,IAAI;AAC9B,EAAAA,kBAAiB,QAAQ,IAAI;AAC/B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,SAAS,gBAAgB;AACvB,MAAI,MAAM;AACV,MAAI,CAAC,SAAS,GAAG,GAAG;AAClB,WAAO;AAAA,EACT;AACA,SAAO,IAAI,cAAc,IAAI,iBAAiB,IAAI;AACpD;AACA,SAAS,wBAAwB;AAC/B,MAAI,IAAI,cAAc;AACtB,MAAI,CAAC,GAAG;AACN,WAAO,CAAC;AAAA,EACV;AACA,SAAO;AAAA,IACL,KAAK,EAAE;AAAA,IACP,MAAM,EAAE;AAAA,IACR,UAAU,EAAE;AAAA,IACZ,UAAU,EAAE;AAAA,IACZ,aAAa,EAAE;AAAA,IACf,eAAe,EAAE;AAAA,EACnB;AACF;AACA,SAAS,aAAa;AACpB,MAAI,KAAK,WAAO,yBAAS,WAAY;AACjC,WAAO,SAAS;AAAA,MACd,OAAO;AAAA,MACP,QAAQ,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU;AAAA,IAC1E,GAAG,sBAAsB,CAAC;AAAA,EAC5B,CAAC,GAAG,CAAC,GACL,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,gCAAU,WAAY;AACpB,QAAI,WAAW,WAAY;AACzB,eAAS,SAAU,WAAW;AAC5B,eAAO,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG;AAAA,UACvC,QAAQ;AAAA,UACR,OAAO,oBAAI,KAAK;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,YAAY,WAAY;AAC1B,eAAS,SAAU,WAAW;AAC5B,eAAO,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG;AAAA,UACvC,QAAQ;AAAA,UACR,OAAO,oBAAI,KAAK;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,qBAAqB,WAAY;AACnC,eAAS,SAAU,WAAW;AAC5B,eAAO,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,sBAAsB,CAAC;AAAA,MAClE,CAAC;AAAA,IACH;AACA,WAAO,iBAAiB,iBAAiB,QAAQ,QAAQ;AACzD,WAAO,iBAAiB,iBAAiB,SAAS,SAAS;AAC3D,QAAI,aAAa,cAAc;AAC/B,mBAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,iBAAiB,iBAAiB,QAAQ,kBAAkB;AAC/H,WAAO,WAAY;AACjB,aAAO,oBAAoB,iBAAiB,QAAQ,QAAQ;AAC5D,aAAO,oBAAoB,iBAAiB,SAAS,SAAS;AAC9D,qBAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,oBAAoB,iBAAiB,QAAQ,kBAAkB;AAAA,IACpI;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AACA,IAAO,qBAAQ;;;ACzEf,IAAAC,iBAAuB;AACvB,IAAI,sBAAsB,SAAU,GAAG,GAAG;AACxC,SAAO,CAAC,OAAO,GAAG,GAAG,CAAC;AACxB;AACA,SAAS,YAAY,OAAO,cAAc;AACxC,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,cAAU,uBAAO,MAAS;AAC9B,MAAI,aAAS,uBAAO,MAAS;AAC7B,MAAI,aAAa,OAAO,SAAS,KAAK,GAAG;AACvC,YAAQ,UAAU,OAAO;AACzB,WAAO,UAAU;AAAA,EACnB;AACA,SAAO,QAAQ;AACjB;AACA,IAAO,sBAAQ;;;AChBf,IAAAC,iBAA+C;AAG/C,IAAI,iBAAiB,SAAU,UAAU,OAAO;AAC9C,MAAI,UAAU,QAAQ;AACpB,YAAQ;AAAA,EACV;AACA,MAAI,OAAO,0BAA0B,aAAa;AAChD,WAAO;AAAA,MACL,IAAI,YAAY,UAAU,KAAK;AAAA,IACjC;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,IAAI;AACrB,MAAI,SAAS;AAAA,IACX,IAAI;AAAA,EACN;AACA,MAAI,OAAO,WAAY;AACrB,QAAI,UAAU,KAAK,IAAI;AACvB,QAAI,UAAU,SAAS,OAAO;AAC5B,eAAS;AACT,cAAQ,KAAK,IAAI;AAAA,IACnB;AACA,WAAO,KAAK,sBAAsB,IAAI;AAAA,EACxC;AACA,SAAO,KAAK,sBAAsB,IAAI;AACtC,SAAO;AACT;AACA,IAAI,mCAAmC,SAAU,GAAG;AAClD,SAAO,OAAO,yBAAyB;AACzC;AACA,IAAI,mBAAmB,SAAU,QAAQ;AACvC,MAAI,iCAAiC,OAAO,EAAE,GAAG;AAC/C,WAAO,cAAc,OAAO,EAAE;AAAA,EAChC;AACA,uBAAqB,OAAO,EAAE;AAChC;AACA,SAAS,eAAe,IAAI,OAAO,SAAS;AAC1C,MAAI,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC1E,MAAI,QAAQ,kBAAU,EAAE;AACxB,MAAI,eAAW,uBAAO,MAAS;AAC/B,MAAI,YAAQ,4BAAY,WAAY;AAClC,QAAI,SAAS,SAAS;AACpB,uBAAiB,SAAS,OAAO;AAAA,IACnC;AAAA,EACF,GAAG,CAAC,CAAC;AACL,gCAAU,WAAY;AACpB,QAAI,CAAC,SAAS,KAAK,KAAK,QAAQ,GAAG;AACjC;AAAA,IACF;AACA,QAAI,WAAW;AACb,YAAM,QAAQ;AAAA,IAChB;AACA,aAAS,UAAU,eAAe,WAAY;AAC5C,YAAM,QAAQ;AAAA,IAChB,GAAG,KAAK;AACR,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AACA,IAAO,yBAAQ;;;AC3Df,IAAAC,iBAA+C;AAG/C,IAAI,gBAAgB,SAAU,UAAU,OAAO;AAC7C,MAAI,UAAU,QAAQ;AACpB,YAAQ;AAAA,EACV;AACA,MAAI,OAAO,0BAA0B,aAAa;AAChD,WAAO;AAAA,MACL,IAAI,WAAW,UAAU,KAAK;AAAA,IAChC;AAAA,EACF;AACA,MAAI,SAAS;AAAA,IACX,IAAI;AAAA,EACN;AACA,MAAI,YAAY,KAAK,IAAI;AACzB,MAAI,OAAO,WAAY;AACrB,QAAI,UAAU,KAAK,IAAI;AACvB,QAAI,UAAU,aAAa,OAAO;AAChC,eAAS;AAAA,IACX,OAAO;AACL,aAAO,KAAK,sBAAsB,IAAI;AAAA,IACxC;AAAA,EACF;AACA,SAAO,KAAK,sBAAsB,IAAI;AACtC,SAAO;AACT;AACA,IAAIC,oCAAmC,SAAU,GAAG;AAClD,SAAO,OAAO,yBAAyB;AACzC;AACA,IAAI,kBAAkB,SAAU,QAAQ;AACtC,MAAIA,kCAAiC,OAAO,EAAE,GAAG;AAC/C,WAAO,aAAa,OAAO,EAAE;AAAA,EAC/B;AACA,uBAAqB,OAAO,EAAE;AAChC;AACA,SAAS,cAAc,IAAI,OAAO;AAChC,MAAI,QAAQ,kBAAU,EAAE;AACxB,MAAI,eAAW,uBAAO,MAAS;AAC/B,MAAI,YAAQ,4BAAY,WAAY;AAClC,QAAI,SAAS,SAAS;AACpB,sBAAgB,SAAS,OAAO;AAAA,IAClC;AAAA,EACF,GAAG,CAAC,CAAC;AACL,gCAAU,WAAY;AACpB,QAAI,CAAC,SAAS,KAAK,KAAK,QAAQ,GAAG;AACjC;AAAA,IACF;AACA,aAAS,UAAU,cAAc,WAAY;AAC3C,YAAM,QAAQ;AAAA,IAChB,GAAG,KAAK;AACR,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AACA,IAAO,wBAAQ;;;ACvDf,IAAAC,iBAAuB;AACvB,2BAA0B;AAI1B,IAAI,WAAW,oBAAI,QAAQ;AAE3B,IAAI,SAAS,oBAAI,QAAQ;AACzB,SAAS,SAAS,YAAY,IAAI;AAChC,MAAI,gBAAgB,SAAS,IAAI,UAAU;AAE3C,MAAI,eAAe;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,IAAI,UAAU,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,IAAI,MAAM,YAAY;AAAA,IAChC,KAAK,SAAU,QAAQ,KAAK,UAAU;AACpC,UAAI,MAAM,QAAQ,IAAI,QAAQ,KAAK,QAAQ;AAE3C,UAAI,aAAa,QAAQ,yBAAyB,QAAQ,GAAG;AAC7D,UAAI,EAAE,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,iBAAiB,EAAE,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,WAAW;AACxK,eAAO;AAAA,MACT;AAGA,iBAAO,qBAAAC,SAAc,GAAG,KAAK,MAAM,QAAQ,GAAG,IAAI,SAAS,KAAK,EAAE,IAAI;AAAA,IACxE;AAAA,IACA,KAAK,SAAU,QAAQ,KAAK,KAAK;AAC/B,UAAI,MAAM,QAAQ,IAAI,QAAQ,KAAK,GAAG;AACtC,SAAG;AACH,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB,SAAU,QAAQ,KAAK;AACrC,UAAI,MAAM,QAAQ,eAAe,QAAQ,GAAG;AAC5C,SAAG;AACH,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,WAAS,IAAI,YAAY,KAAK;AAC9B,SAAO,IAAI,OAAO,UAAU;AAC5B,SAAO;AACT;AACA,SAAS,YAAY,cAAc;AACjC,MAAI,SAAS,kBAAU;AACvB,MAAI,eAAW,uBAAO,YAAY;AAClC,MAAI,QAAQ,oBAAY,WAAY;AAClC,WAAO,SAAS,SAAS,SAAS,WAAY;AAC5C,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AACA,IAAO,sBAAQ;;;ACvDf,IAAAC,iBAAiC;AAIjC,IAAI,gBAAgB,SAAU,cAAc;AAC1C,MAAI,sBAAkB,uBAAO,YAAY;AACzC,MAAI,mBAAmB,oBAAY,WAAY;AAC7C,WAAO,WAAW,gBAAgB,OAAO,IAAI,gBAAgB,QAAQ,IAAI,gBAAgB;AAAA,EAC3F,GAAG,CAAC,CAAC;AACL,MAAI,KAAK,WAAO,yBAAS,gBAAgB,GAAG,CAAC,GAC3C,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,aAAa,sBAAc,WAAY;AACzC,aAAS,gBAAgB;AAAA,EAC3B,CAAC;AACD,SAAO,CAAC,OAAO,UAAU,UAAU;AACrC;AACA,IAAO,wBAAQ;;;ACjBf,IAAAC,iBAAoC;AAEpC,IAAI,cAAc,oBAAI,IAAI;AAC1B,IAAI;AACJ,IAAI,mBAAmB;AAAA,EACrB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,SAAS,eAAe;AACtB,MAAI,KAAK;AACT,MAAI,UAAU;AACd,YAAU;AACV,MAAI,YAAY,MAAM;AACpB;AAAA,EACF;AACA,MAAI;AACF,aAAS,gBAAgB,SAAS,WAAW,GAAG,kBAAkB,cAAc,KAAK,GAAG,CAAC,gBAAgB,MAAM,kBAAkB,cAAc,KAAK,GAAG;AACrJ,UAAI,aAAa,gBAAgB;AACjC,iBAAW;AAAA,IACb;AAAA,EACF,SAAS,OAAO;AACd,UAAM;AAAA,MACJ,OAAO;AAAA,IACT;AAAA,EACF,UAAE;AACA,QAAI;AACF,UAAI,mBAAmB,CAAC,gBAAgB,SAAS,KAAK,cAAc;AAAS,WAAG,KAAK,aAAa;AAAA,IACpG,UAAE;AACA,UAAI;AAAK,cAAM,IAAI;AAAA,IACrB;AAAA,EACF;AACF;AACA,IAAI,YAAY;AAChB,SAAS,YAAY;AACnB,MAAI,KAAK;AACT,MAAI,QAAQ,OAAO;AACnB,MAAI,UAAU,CAAC;AACf,MAAI,eAAe;AACnB,MAAI;AACF,aAAS,KAAK,SAAS,OAAO,KAAK,gBAAgB,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AAC/F,UAAI,MAAM,GAAG;AACb,cAAQ,GAAG,IAAI,SAAS,iBAAiB,GAAG;AAC5C,UAAI,QAAQ,GAAG,MAAM,KAAK,GAAG,GAAG;AAC9B,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,UAAM;AAAA,MACJ,OAAO;AAAA,IACT;AAAA,EACF,UAAE;AACA,QAAI;AACF,UAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG;AAAS,WAAG,KAAK,EAAE;AAAA,IACpD,UAAE;AACA,UAAI;AAAK,cAAM,IAAI;AAAA,IACrB;AAAA,EACF;AACA,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACF;AACO,SAAS,iBAAiB,QAAQ;AACvC,qBAAmB;AACnB,MAAI;AAAM,cAAU;AACtB;AACA,SAAS,gBAAgB;AACvB,MAAI,qBAAa,CAAC,WAAW;AAC3B,WAAO,CAAC;AACR,cAAU;AACV,WAAO,iBAAiB,UAAU,YAAY;AAC9C,gBAAY;AAAA,EACd;AACA,MAAI,KAAK,WAAO,yBAAS,IAAI,GAAG,CAAC,GAC/B,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,gCAAU,WAAY;AACpB,QAAI,CAAC,mBAAW;AACd;AAAA,IACF;AAGA,QAAI,CAAC,WAAW;AACd,aAAO,iBAAiB,UAAU,YAAY;AAAA,IAChD;AACA,QAAI,aAAa,WAAY;AAC3B,eAAS,IAAI;AAAA,IACf;AACA,gBAAY,IAAI,UAAU;AAC1B,WAAO,WAAY;AACjB,kBAAY,OAAO,UAAU;AAC7B,UAAI,YAAY,SAAS,GAAG;AAC1B,eAAO,oBAAoB,UAAU,YAAY;AACjD,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AACA,IAAO,wBAAQ;;;ACrGf,IAAAC,iBAAsC;;;ACDtC,IAAAC,iBAAkC;AAClC,IAAI,kBAAkB,WAAY;AAChC,MAAI,mBAAe,uBAAO,KAAK;AAC/B,gCAAU,WAAY;AACpB,iBAAa,UAAU;AACvB,WAAO,WAAY;AACjB,mBAAa,UAAU;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AACA,IAAO,0BAAQ;;;ADRf,SAAS,aAAa,cAAc;AAClC,MAAI,eAAe,wBAAgB;AACnC,MAAI,KAAK,WAAO,yBAAS,YAAY,GAAG,CAAC,GACvC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,sBAAkB,4BAAY,SAAU,cAAc;AAExD,QAAI,aAAa,SAAS;AACxB;AAAA,IACF;AACA,aAAS,YAAY;AAAA,EACvB,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,OAAO,eAAe;AAChC;AACA,IAAO,uBAAQ;;;AEZf,SAAS,UAAU,QAAQ,cAAc;AACvC,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe,WAAY;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,KAAK,OAAO,oBAAY,GAAG,CAAC,GAC9B,WAAW,GAAG,CAAC,GACf,cAAc,GAAG,CAAC;AACpB,MAAI,kBAAkB,kBAAU,YAAY;AAC5C,8BAAoB,WAAY;AAC9B,QAAI,KAAK,iBAAiB,QAAQ,QAAQ;AAC1C,QAAI,CAAC,IAAI;AACP;AAAA,IACF;AACA,QAAI,iBAAiB,WAAY;AAC/B,UAAI;AACJ,UAAI,OAAO,UAAU;AACnB,YAAI,SAAS,kBAAkB;AAC7B,wBAAc;AAAA,YACZ,MAAM,SAAS,iBAAiB;AAAA,YAChC,KAAK,SAAS,iBAAiB;AAAA,UACjC;AAAA,QACF,OAAO;AAIL,wBAAc;AAAA,YACZ,MAAM,KAAK,IAAI,OAAO,aAAa,SAAS,gBAAgB,YAAY,SAAS,KAAK,UAAU;AAAA,YAChG,KAAK,KAAK,IAAI,OAAO,aAAa,SAAS,gBAAgB,WAAW,SAAS,KAAK,SAAS;AAAA,UAC/F;AAAA,QACF;AAAA,MACF,OAAO;AACL,sBAAc;AAAA,UACZ,MAAM,GAAG;AAAA,UACT,KAAK,GAAG;AAAA,QACV;AAAA,MACF;AACA,UAAI,gBAAgB,QAAQ,WAAW,GAAG;AACxC,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF;AACA,mBAAe;AACf,OAAG,iBAAiB,UAAU,cAAc;AAC5C,WAAO,WAAY;AACjB,SAAG,oBAAoB,UAAU,cAAc;AAAA,IACjD;AAAA,EACF,GAAG,CAAC,GAAG,MAAM;AACb,SAAO;AACT;AACA,IAAO,oBAAQ;;;ACtDf,IAAAC,wBAA0B;AAG1B,IAAAC,iBAAkC;AAClC,SAAS,cAAc,OAAO,SAAS;AACrC,MAAI,IAAI;AACR,MAAI,kBAAkB,CAAC;AACvB,MAAI;AACJ,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,sBAAkB;AAAA,EACpB,eAAW,sBAAAC,SAAc,OAAO,GAAG;AACjC,uBAAmB,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,qBAAqB,QAAQ,OAAO,SAAS,KAAK;AACpI,eAAW,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa,QAAQ,OAAO,SAAS,KAAK;AAAA,EACtH;AACA,MAAI,SAAS,SAAU,MAAM;AAC3B,QAAI,WAAW,OAAO,GAAG;AACvB,aAAO,QAAQ,IAAI;AAAA,IACrB;AACA,QAAI,SAAS,OAAO,SAAK,sBAAAA,SAAc,IAAI,GAAG;AAC5C,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACA,MAAI,KAAK,WAAO,yBAAS,eAAe,GAAG,CAAC,GAC1C,WAAW,GAAG,CAAC,GACf,cAAc,GAAG,CAAC;AACpB,MAAI,kBAAc,wBAAQ,WAAY;AACpC,QAAI,eAAe,oBAAI,IAAI;AAC3B,QAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,aAAS,QAAQ,SAAU,MAAM;AAC/B,mBAAa,IAAI,OAAO,IAAI,GAAG,IAAI;AAAA,IACrC,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,aAAa,SAAU,MAAM;AAC/B,WAAO,YAAY,IAAI,OAAO,IAAI,CAAC;AAAA,EACrC;AACA,MAAI,SAAS,SAAU,MAAM;AAC3B,gBAAY,IAAI,OAAO,IAAI,GAAG,IAAI;AAClC,gBAAY,MAAM,KAAK,YAAY,OAAO,CAAC,CAAC;AAAA,EAC9C;AACA,MAAI,WAAW,SAAU,MAAM;AAC7B,gBAAY,OAAO,OAAO,IAAI,CAAC;AAC/B,gBAAY,MAAM,KAAK,YAAY,OAAO,CAAC,CAAC;AAAA,EAC9C;AACA,MAAI,SAAS,SAAU,MAAM;AAC3B,QAAI,WAAW,IAAI,GAAG;AACpB,eAAS,IAAI;AAAA,IACf,OAAO;AACL,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACA,MAAI,YAAY,WAAY;AAC1B,UAAM,QAAQ,SAAU,MAAM;AAC5B,kBAAY,IAAI,OAAO,IAAI,GAAG,IAAI;AAAA,IACpC,CAAC;AACD,gBAAY,MAAM,KAAK,YAAY,OAAO,CAAC,CAAC;AAAA,EAC9C;AACA,MAAI,cAAc,WAAY;AAC5B,UAAM,QAAQ,SAAU,MAAM;AAC5B,kBAAY,OAAO,OAAO,IAAI,CAAC;AAAA,IACjC,CAAC;AACD,gBAAY,MAAM,KAAK,YAAY,OAAO,CAAC,CAAC;AAAA,EAC9C;AACA,MAAI,mBAAe,wBAAQ,WAAY;AACrC,WAAO,MAAM,MAAM,SAAU,MAAM;AACjC,aAAO,CAAC,YAAY,IAAI,OAAO,IAAI,CAAC;AAAA,IACtC,CAAC;AAAA,EACH,GAAG,CAAC,OAAO,WAAW,CAAC;AACvB,MAAI,kBAAc,wBAAQ,WAAY;AACpC,WAAO,MAAM,MAAM,SAAU,MAAM;AACjC,aAAO,YAAY,IAAI,OAAO,IAAI,CAAC;AAAA,IACrC,CAAC,KAAK,CAAC;AAAA,EACT,GAAG,CAAC,OAAO,aAAa,YAAY,CAAC;AACrC,MAAI,wBAAoB,wBAAQ,WAAY;AAC1C,WAAO,CAAC,gBAAgB,CAAC;AAAA,EAC3B,GAAG,CAAC,cAAc,WAAW,CAAC;AAC9B,MAAI,YAAY,WAAY;AAC1B,WAAO,cAAc,YAAY,IAAI,UAAU;AAAA,EACjD;AACA,MAAI,WAAW,WAAY;AACzB,gBAAY,MAAM;AAClB,gBAAY,CAAC,CAAC;AAAA,EAChB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,sBAAc,MAAM;AAAA,IAC5B,UAAU,sBAAc,QAAQ;AAAA,IAChC,QAAQ,sBAAc,MAAM;AAAA,IAC5B,WAAW,sBAAc,SAAS;AAAA,IAClC,aAAa,sBAAc,WAAW;AAAA,IACtC,UAAU,sBAAc,QAAQ;AAAA,IAChC,WAAW,sBAAc,SAAS;AAAA,EACpC;AACF;AACA,IAAO,wBAAQ;;;ACrGf,IAAI,yBAAyB,sBAAsB,WAAY;AAC7D,SAAO,oBAAY,iBAAiB;AACtC,CAAC;AACD,IAAO,iCAAQ;;;ACJf,IAAAC,iBAAyB;AAEzB,SAAS,OAAO,cAAc;AAC5B,MAAI,eAAe,WAAY;AAC7B,WAAO,IAAI,IAAI,YAAY;AAAA,EAC7B;AACA,MAAI,KAAK,WAAO,yBAAS,YAAY,GAAG,CAAC,GACvC,MAAM,GAAG,CAAC,GACV,SAAS,GAAG,CAAC;AACf,MAAI,MAAM,SAAU,KAAK;AACvB,QAAI,IAAI,IAAI,GAAG,GAAG;AAChB;AAAA,IACF;AACA,WAAO,SAAU,SAAS;AACxB,UAAI,OAAO,IAAI,IAAI,OAAO;AAC1B,WAAK,IAAI,GAAG;AACZ,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,SAAS,SAAU,KAAK;AAC1B,QAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACjB;AAAA,IACF;AACA,WAAO,SAAU,SAAS;AACxB,UAAI,OAAO,IAAI,IAAI,OAAO;AAC1B,WAAK,OAAO,GAAG;AACf,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,WAAY;AACtB,WAAO,OAAO,aAAa,CAAC;AAAA,EAC9B;AACA,SAAO,CAAC,KAAK;AAAA,IACX,KAAK,sBAAc,GAAG;AAAA,IACtB,QAAQ,sBAAc,MAAM;AAAA,IAC5B,OAAO,sBAAc,KAAK;AAAA,EAC5B,CAAC;AACH;AACA,IAAO,iBAAQ;;;ACtCf,IAAAC,iBAAyB;AAGzB,IAAI,cAAc,SAAU,cAAc;AACxC,MAAI,KAAK,WAAO,yBAAS,YAAY,GAAG,CAAC,GACvC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,gBAAgB,sBAAc,SAAU,OAAO;AACjD,aAAS,SAAU,WAAW;AAC5B,UAAI,WAAW,WAAW,KAAK,IAAI,MAAM,SAAS,IAAI;AACtD,aAAO,WAAW,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,QAAQ,IAAI;AAAA,IAClE,CAAC;AAAA,EACH,CAAC;AACD,SAAO,CAAC,OAAO,aAAa;AAC9B;AACA,IAAO,sBAAQ;;;AChBf,IAAAC,iBAAgC;AAEhC,IAAIC,uBAAsB,+BAAuB,8BAAe;AAChE,IAAO,oCAAQA;;;ACAf,IAAI,sCAAsC,oBAAY,oCAA4B;AAClF,IAAO,8CAAQ;;;ACCf,SAAS,QAAQ,QAAQ;AACvB,MAAI,KAAK,OAAO,oBAAY,WAAY;AACpC,QAAI,KAAK,iBAAiB,MAAM;AAChC,WAAO,KAAK;AAAA,MACV,OAAO,GAAG;AAAA,MACV,QAAQ,GAAG;AAAA,IACb,IAAI;AAAA,EACN,CAAC,GAAG,CAAC,GACL,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,8CAAoC,WAAY;AAC9C,QAAI,KAAK,iBAAiB,MAAM;AAChC,QAAI,CAAC,IAAI;AACP;AAAA,IACF;AACA,QAAI,iBAAiB,IAAI,0BAAe,SAAU,SAAS;AACzD,cAAQ,QAAQ,SAAU,OAAO;AAC/B,YAAIC,MAAK,MAAM,QACb,cAAcA,IAAG,aACjB,eAAeA,IAAG;AACpB,iBAAS;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,mBAAe,QAAQ,EAAE;AACzB,WAAO,WAAY;AACjB,qBAAe,WAAW;AAAA,IAC5B;AAAA,EACF,GAAG,CAAC,GAAG,MAAM;AACb,SAAO;AACT;AACA,IAAO,kBAAQ;;;ACrCf,IAAAC,iBAAiC;AAGjC,IAAI,WAAW;AAAA,EACb,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAIC,aAAY,SAAS;AAAA,EACvB,MAAM;AACR,GAAG,QAAQ;AACX,SAAS,qBAAqB,WAAW;AACvC,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,MAAI,UAAU,aAAa,GAAG;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,UAAU,WAAW,CAAC;AAClC,MAAI,KAAK,MAAM,sBAAsB,GACnC,SAAS,GAAG,QACZ,QAAQ,GAAG,OACX,MAAM,GAAG,KACT,OAAO,GAAG,MACV,QAAQ,GAAG,OACX,SAAS,GAAG;AACd,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,QAAQ;AAChC,MAAI,KAAK,WAAO,yBAASA,UAAS,GAAG,CAAC,GACpC,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,MAAI,eAAW,uBAAO,KAAK;AAC3B,MAAI,mBAAe,uBAAO,KAAK;AAC/B,WAAS,UAAU;AACnB,8BAAoB,WAAY;AAC9B,QAAI,KAAK,iBAAiB,QAAQ,QAAQ;AAC1C,QAAI,CAAC,IAAI;AACP;AAAA,IACF;AACA,QAAI,iBAAiB,WAAY;AAC/B,UAAI,SAAS;AACb,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI,CAAC,OAAO,cAAc;AACxB;AAAA,MACF;AACA,eAAS,OAAO,aAAa;AAC7B,aAAO,SAAS,OAAO,SAAS,IAAI;AACpC,UAAI,QAAQ,aAAa,SAAS;AAChC,eAAO,qBAAqB,MAAM;AAClC,iBAAS,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,UAC9C;AAAA,QACF,CAAC,GAAG,IAAI,CAAC;AAAA,MACX;AAAA,IACF;AAEA,QAAI,mBAAmB,SAAU,GAAG;AAElC,UAAI,EAAE,WAAW,GAAG;AAClB;AAAA,MACF;AACA,UAAI,CAAC,OAAO,cAAc;AACxB;AAAA,MACF;AACA,UAAI,SAAS,QAAQ,MAAM;AACzB,iBAAS,SAAS,CAAC,GAAGA,UAAS,CAAC;AAAA,MAClC;AACA,mBAAa,UAAU;AACvB,UAAI,SAAS,OAAO,aAAa;AACjC,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,aAAO,gBAAgB;AACvB,mBAAa,UAAU,GAAG,SAAS,EAAE,MAAM;AAAA,IAC7C;AACA,OAAG,iBAAiB,WAAW,cAAc;AAC7C,aAAS,iBAAiB,aAAa,gBAAgB;AACvD,WAAO,WAAY;AACjB,SAAG,oBAAoB,WAAW,cAAc;AAChD,eAAS,oBAAoB,aAAa,gBAAgB;AAAA,IAC5D;AAAA,EACF,GAAG,CAAC,GAAG,MAAM;AACb,SAAO;AACT;AACA,IAAO,2BAAQ;;;AC/Ff,IAAAC,iBAAoC;;;ACApC,IAAAC,mBAAqB;AACrB,IAAAC,iBAAwB;AAKxB,SAAS,cAAc,IAAI,SAAS;AAClC,MAAI;AACJ,MAAI,eAAO;AACT,QAAI,CAAC,WAAW,EAAE,GAAG;AACnB,cAAQ,MAAM,uDAAuD,OAAO,OAAO,EAAE,CAAC;AAAA,IACxF;AAAA,EACF;AACA,MAAI,QAAQ,kBAAU,EAAE;AACxB,MAAI,QAAQ,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK;AAClH,MAAI,gBAAY,wBAAQ,WAAY;AAClC,eAAO,iBAAAC,SAAS,WAAY;AAC1B,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MACzB;AACA,aAAO,MAAM,QAAQ,MAAM,OAAO,cAAc,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAAA,IAC1E,GAAG,MAAM,OAAO;AAAA,EAClB,GAAG,CAAC,CAAC;AACL,qBAAW,WAAY;AACrB,cAAU,OAAO;AAAA,EACnB,CAAC;AACD,SAAO;AAAA,IACL,KAAK;AAAA,IACL,QAAQ,UAAU;AAAA,IAClB,OAAO,UAAU;AAAA,EACnB;AACF;AACA,IAAO,wBAAQ;;;AD/Bf,SAAS,YAAY,OAAO,SAAS;AACnC,MAAI,KAAK,WAAO,yBAAS,KAAK,GAAG,CAAC,GAChC,YAAY,GAAG,CAAC,GAChB,eAAe,GAAG,CAAC;AACrB,MAAI,MAAM,sBAAc,WAAY;AAClC,iBAAa,KAAK;AAAA,EACpB,GAAG,OAAO,EAAE;AACZ,gCAAU,WAAY;AACpB,QAAI;AAAA,EACN,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AACA,IAAO,sBAAQ;;;AEdf,IAAAC,iBAAoC;AAGpC,SAAS,kBAAkB,QAAQ,MAAM,SAAS;AAChD,MAAI,KAAK,WAAO,yBAAS,CAAC,CAAC,GAAG,CAAC,GAC7B,OAAO,GAAG,CAAC,GACX,UAAU,GAAG,CAAC;AAChB,MAAI,MAAM,sBAAc,WAAY;AAClC,YAAQ,CAAC,CAAC;AAAA,EACZ,GAAG,OAAO,EAAE;AACZ,gCAAU,WAAY;AACpB,WAAO,IAAI;AAAA,EACb,GAAG,IAAI;AACP,0BAAgB,QAAQ,CAAC,IAAI,CAAC;AAChC;AACA,IAAO,4BAAQ;;;AChBf,IAAAC,iBAA+C;AAG/C,IAAI,aAAa,SAAU,IAAI,OAAO;AACpC,MAAI,gBAAgB,sBAAc,EAAE;AACpC,MAAI,eAAW,uBAAO,IAAI;AAC1B,MAAI,YAAQ,4BAAY,WAAY;AAClC,QAAI,SAAS,SAAS;AACpB,mBAAa,SAAS,OAAO;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,gCAAU,WAAY;AACpB,QAAI,CAAC,SAAS,KAAK,KAAK,QAAQ,GAAG;AACjC;AAAA,IACF;AACA,aAAS,UAAU,WAAW,eAAe,KAAK;AAClD,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AACA,IAAO,qBAAQ;;;ACpBf,IAAAC,iBAAkC;AAGlC,IAAI,kBAAkB;AAAA,EACpB,kBAAkB;AACpB;AACA,SAAS,SAAS,OAAO,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,eAAW,uBAAO,oBAAY,SAAS,QAAQ,EAAE;AACrD,gCAAU,WAAY;AACpB,aAAS,QAAQ;AAAA,EACnB,GAAG,CAAC,KAAK,CAAC;AACV,qBAAW,WAAY;AACrB,QAAI,QAAQ,kBAAkB;AAC5B,eAAS,QAAQ,SAAS;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AACA,IAAO,mBAAQ;;;ACpBf,IAAAC,iBAAkC;AAClC,IAAI,cAAc,SAAU,OAAO,OAAO;AAIxC,SAAO,QAAQ,MAAM,IAAI,SAAU,GAAG,KAAK;AACzC,WAAO,CAAC,OAAO,GAAG,MAAM,GAAG,GAAG,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,GAAG,CAAC,IAAI,MAAM;AAAA,EAClG,CAAC,EAAE,OAAO,SAAU,KAAK;AACvB,WAAO,OAAO;AAAA,EAChB,CAAC,IAAI,QAAQ,MAAM,IAAI,SAAU,GAAG,KAAK;AACvC,WAAO;AAAA,EACT,CAAC,IAAI,CAAC;AACR;AACA,IAAI,mBAAmB,SAAU,QAAQ,MAAM;AAC7C,MAAI,sBAAkB,uBAAO,MAAS;AACtC,gCAAU,WAAY;AACpB,QAAI,UAAU,YAAY,gBAAgB,SAAS,IAAI;AACvD,QAAI,eAAe,gBAAgB;AACnC,oBAAgB,UAAU;AAC1B,WAAO,OAAO,SAAS,cAAc,IAAI;AAAA,EAC3C,GAAG,IAAI;AACT;AACA,IAAO,2BAAQ;;;ACtBf,IAAAC,iBAAgC;AAEhC,IAAO,gCAAQ,mBAAmB,8BAAe;;;ACDjD,IAAAC,iBAAqD;AAQrD,IAAI,iBAAiB,SAAU,MAAM,SAAS;AAC5C,MAAI,kBAAkB,QAAQ,iBAC5B,gBAAgB,QAAQ,eACxB,aAAa,QAAQ,YACrB,KAAK,QAAQ,UACb,WAAW,OAAO,SAAS,IAAI;AACjC,MAAI,gBAAgB,kBAAU,UAAU;AACxC,MAAI,OAAO,gBAAQ,eAAe;AAClC,MAAI,kCAA8B,uBAAO,KAAK;AAC9C,MAAI,KAAK,WAAO,yBAAS,CAAC,CAAC,GAAG,CAAC,GAC7B,aAAa,GAAG,CAAC,GACjB,gBAAgB,GAAG,CAAC;AACtB,MAAI,KAAK,WAAO,yBAAS,CAAC,CAAC,GAAG,CAAC,GAC7B,eAAe,GAAG,CAAC,GACnB,kBAAkB,GAAG,CAAC;AACxB,MAAI,kBAAkB,SAAU,iBAAiB,WAAW;AAC1D,QAAI,SAAS,cAAc,OAAO,GAAG;AACnC,aAAO,KAAK,KAAK,kBAAkB,cAAc,OAAO;AAAA,IAC1D;AACA,QAAI,MAAM;AACV,QAAI,WAAW;AACf,aAAS,IAAI,WAAW,IAAI,KAAK,QAAQ,KAAK;AAC5C,UAAI,SAAS,cAAc,QAAQ,GAAG,KAAK,CAAC,CAAC;AAC7C,aAAO;AACP,iBAAW;AACX,UAAI,OAAO,iBAAiB;AAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,YAAY,SAAU,WAAW;AACnC,QAAI,SAAS,cAAc,OAAO,GAAG;AACnC,aAAO,KAAK,MAAM,YAAY,cAAc,OAAO;AAAA,IACrD;AACA,QAAI,MAAM;AACV,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,SAAS,cAAc,QAAQ,GAAG,KAAK,CAAC,CAAC;AAC7C,aAAO;AACP,UAAI,OAAO,WAAW;AACpB,iBAAS;AACT;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS;AAAA,EAClB;AAEA,MAAI,iBAAiB,SAAU,OAAO;AACpC,QAAI,SAAS,cAAc,OAAO,GAAG;AACnC,UAAI,WAAW,QAAQ,cAAc;AACrC,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK,MAAM,GAAG,KAAK,EAAE,OAAO,SAAU,KAAK,GAAG,GAAG;AAC5D,aAAO,MAAM,cAAc,QAAQ,GAAG,KAAK,CAAC,CAAC;AAAA,IAC/C,GAAG,CAAC;AACJ,WAAO;AAAA,EACT;AACA,MAAI,kBAAc,wBAAQ,WAAY;AACpC,QAAI,SAAS,cAAc,OAAO,GAAG;AACnC,aAAO,KAAK,SAAS,cAAc;AAAA,IACrC;AACA,WAAO,KAAK,OAAO,SAAU,KAAK,GAAG,OAAO;AAC1C,aAAO,MAAM,cAAc,QAAQ,OAAO,KAAK,KAAK,CAAC;AAAA,IACvD,GAAG,CAAC;AAAA,EACN,GAAG,CAAC,IAAI,CAAC;AACT,MAAI,iBAAiB,WAAY;AAC/B,QAAI,YAAY,iBAAiB,eAAe;AAChD,QAAI,WAAW;AACb,UAAI,YAAY,UAAU,WACxB,eAAe,UAAU;AAC3B,UAAI,SAAS,UAAU,SAAS;AAChC,UAAI,eAAe,gBAAgB,cAAc,MAAM;AACvD,UAAI,UAAU,KAAK,IAAI,GAAG,SAAS,QAAQ;AAC3C,UAAI,MAAM,KAAK,IAAI,KAAK,QAAQ,SAAS,eAAe,QAAQ;AAChE,UAAI,YAAY,eAAe,OAAO;AACtC,sBAAgB;AAAA,QACd,QAAQ,cAAc,YAAY;AAAA,QAClC,WAAW,YAAY;AAAA,MACzB,CAAC;AACD,oBAAc,KAAK,MAAM,SAAS,GAAG,EAAE,IAAI,SAAU,KAAK,OAAO;AAC/D,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO,QAAQ;AAAA,QACjB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,0BAAgB,WAAY;AAC1B,QAAI,UAAU,iBAAiB,aAAa;AAC5C,QAAI,SAAS;AACX,aAAO,KAAK,YAAY,EAAE,QAAQ,SAAU,KAAK;AAC/C,eAAO,QAAQ,MAAM,GAAG,IAAI,aAAa,GAAG;AAAA,MAC9C,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,gCAAU,WAAY;AACpB,QAAI,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,UAAU,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,SAAS;AAC3H;AAAA,IACF;AACA,mBAAe;AAAA,EACjB,GAAG,CAAC,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,QAAQ,IAAI,CAAC;AAC1H,2BAAiB,UAAU,SAAU,GAAG;AACtC,QAAI,4BAA4B,SAAS;AACvC,kCAA4B,UAAU;AACtC;AAAA,IACF;AACA,MAAE,eAAe;AACjB,mBAAe;AAAA,EACjB,GAAG;AAAA,IACD,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,WAAW,SAAU,OAAO;AAC9B,QAAI,YAAY,iBAAiB,eAAe;AAChD,QAAI,WAAW;AACb,kCAA4B,UAAU;AACtC,gBAAU,YAAY,eAAe,KAAK;AAC1C,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,SAAO,CAAC,YAAY,sBAAc,QAAQ,CAAC;AAC7C;AACA,IAAO,yBAAQ;;;AClIf,IAAAC,iBAA4C;AAIrC,IAAI;AAAA,CACV,SAAUC,aAAY;AACrB,EAAAA,YAAWA,YAAW,YAAY,IAAI,CAAC,IAAI;AAC3C,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AACrC,EAAAA,YAAWA,YAAW,SAAS,IAAI,CAAC,IAAI;AACxC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACzC,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,SAAS,aAAa,WAAW,SAAS;AACxC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,KAAK,QAAQ,gBACf,iBAAiB,OAAO,SAAS,IAAI,IACrC,KAAK,QAAQ,mBACb,oBAAoB,OAAO,SAAS,IAAI,MAAO,IAC/C,KAAK,QAAQ,QACb,SAAS,OAAO,SAAS,QAAQ,IACjC,SAAS,QAAQ,QACjB,UAAU,QAAQ,SAClB,YAAY,QAAQ,WACpB,UAAU,QAAQ,SAClB,YAAY,QAAQ;AACtB,MAAI,YAAY,kBAAU,MAAM;AAChC,MAAI,aAAa,kBAAU,OAAO;AAClC,MAAI,eAAe,kBAAU,SAAS;AACtC,MAAI,aAAa,kBAAU,OAAO;AAClC,MAAI,wBAAoB,uBAAO,CAAC;AAChC,MAAI,wBAAoB,uBAAO,MAAS;AACxC,MAAI,mBAAe,uBAAO,MAAS;AACnC,MAAI,KAAK,WAAO,yBAAS,GAAG,CAAC,GAC3B,gBAAgB,GAAG,CAAC,GACpB,mBAAmB,GAAG,CAAC;AACzB,MAAI,KAAK,WAAO,yBAAS,WAAW,MAAM,GAAG,CAAC,GAC5C,aAAa,GAAG,CAAC,GACjB,gBAAgB,GAAG,CAAC;AACtB,MAAI,YAAY,WAAY;AAC1B,QAAIC;AACJ,QAAI,kBAAkB,UAAU,oBAAoBA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,gBAAgB,WAAW,MAAM;AACtJ,UAAI,kBAAkB,SAAS;AAC7B,qBAAa,kBAAkB,OAAO;AAAA,MACxC;AACA,wBAAkB,UAAU,WAAW,WAAY;AAEjD,kBAAU;AACV,0BAAkB;AAAA,MACpB,GAAG,iBAAiB;AAAA,IACtB;AAAA,EACF;AACA,MAAI,YAAY,WAAY;AAC1B,QAAI,kBAAkB,SAAS;AAC7B,mBAAa,kBAAkB,OAAO;AAAA,IACxC;AACA,QAAI,aAAa,SAAS;AACxB,mBAAa,QAAQ,MAAM;AAAA,IAC7B;AACA,QAAI,KAAK,IAAI,UAAU,WAAW,SAAS;AAC3C,kBAAc,WAAW,UAAU;AACnC,OAAG,UAAU,SAAU,OAAO;AAC5B,UAAIA;AACJ,UAAI,aAAa,YAAY,IAAI;AAC/B;AAAA,MACF;AACA,gBAAU;AACV,OAACA,MAAK,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,YAAY,OAAO,EAAE;AAC5F,oBAAc,GAAG,cAAc,WAAW,MAAM;AAAA,IAClD;AACA,OAAG,SAAS,SAAU,OAAO;AAC3B,UAAIA;AACJ,UAAI,aAAa,YAAY,IAAI;AAC/B;AAAA,MACF;AACA,OAACA,MAAK,UAAU,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,OAAO,EAAE;AAC1F,wBAAkB,UAAU;AAC5B,oBAAc,GAAG,cAAc,WAAW,IAAI;AAAA,IAChD;AACA,OAAG,YAAY,SAAU,SAAS;AAChC,UAAIA;AACJ,UAAI,aAAa,YAAY,IAAI;AAC/B;AAAA,MACF;AACA,OAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc,SAAS,EAAE;AAClG,uBAAiB,OAAO;AAAA,IAC1B;AACA,OAAG,UAAU,SAAU,OAAO;AAC5B,UAAIA;AACJ,OAACA,MAAK,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,YAAY,OAAO,EAAE;AAE5F,UAAI,aAAa,YAAY,IAAI;AAC/B,kBAAU;AAAA,MACZ;AAEA,UAAI,CAAC,aAAa,WAAW,aAAa,YAAY,IAAI;AACxD,sBAAc,GAAG,cAAc,WAAW,MAAM;AAAA,MAClD;AAAA,IACF;AACA,iBAAa,UAAU;AAAA,EACzB;AACA,MAAI,cAAc,SAAU,SAAS;AACnC,QAAIA;AACJ,QAAI,eAAe,WAAW,MAAM;AAClC,OAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,OAAO;AAAA,IAClF,OAAO;AACL,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AAAA,EACF;AACA,MAAI,UAAU,WAAY;AACxB,sBAAkB,UAAU;AAC5B,cAAU;AAAA,EACZ;AACA,MAAI,aAAa,WAAY;AAC3B,QAAIA;AACJ,QAAI,kBAAkB,SAAS;AAC7B,mBAAa,kBAAkB,OAAO;AAAA,IACxC;AACA,sBAAkB,UAAU;AAC5B,KAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAM;AAC1E,iBAAa,UAAU;AAAA,EACzB;AACA,gCAAU,WAAY;AACpB,QAAI,CAAC,UAAU,WAAW;AACxB,cAAQ;AAAA,IACV;AAAA,EACF,GAAG,CAAC,WAAW,MAAM,CAAC;AACtB,qBAAW,WAAY;AACrB,eAAW;AAAA,EACb,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA,aAAa,sBAAc,WAAW;AAAA,IACtC,SAAS,sBAAc,OAAO;AAAA,IAC9B,YAAY,sBAAc,UAAU;AAAA,IACpC;AAAA,IACA,cAAc,aAAa;AAAA,EAC7B;AACF;AACA,IAAO,uBAAQ;;;AC3If,IAAAC,iBAAkC;AAClC,SAAS,mBAAmB,eAAe,OAAO;AAChD,MAAI,gBAAY,uBAAO,CAAC,CAAC;AACzB,gCAAU,WAAY;AACpB,QAAI,UAAU,SAAS;AACrB,UAAI,UAAU,OAAO,KAAK,SAAS,SAAS,CAAC,GAAG,UAAU,OAAO,GAAG,KAAK,CAAC;AAC1E,UAAI,iBAAiB,CAAC;AACtB,cAAQ,QAAQ,SAAU,KAAK;AAC7B,YAAI,CAAC,OAAO,GAAG,UAAU,QAAQ,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG;AAClD,yBAAe,GAAG,IAAI;AAAA,YACpB,MAAM,UAAU,QAAQ,GAAG;AAAA,YAC3B,IAAI,MAAM,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,OAAO,KAAK,cAAc,EAAE,QAAQ;AACtC,gBAAQ,IAAI,wBAAwB,eAAe,cAAc;AAAA,MACnE;AAAA,IACF;AACA,cAAU,UAAU;AAAA,EACtB,CAAC;AACH;AACA,IAAO,6BAAQ;;;ACpBf,IAAI,sBAAsB,SAAU,UAAU,QAAQ,SAAS;AAC7D,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,cAAc,kBAAU,QAAQ;AACpC,mCAA+B,WAAY;AACzC,QAAI,UAAU,iBAAiB,MAAM;AACrC,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAIC,YAAW,IAAI,iBAAiB,YAAY,OAAO;AACvD,IAAAA,UAAS,QAAQ,SAAS,OAAO;AACjC,WAAO,WAAY;AACjB,MAAAA,cAAa,QAAQA,cAAa,SAAS,SAASA,UAAS,WAAW;AAAA,IAC1E;AAAA,EACF,GAAG,CAAC,OAAO,GAAG,MAAM;AACtB;AACA,IAAO,8BAAQ;;;ACnBf,IAAAC,iBAAoC;AAG7B,IAAI;AAAA,CACV,SAAUC,YAAW;AACpB,EAAAA,WAAU,OAAO,IAAI;AACrB,EAAAA,WAAU,MAAM,IAAI;AACpB,EAAAA,WAAU,QAAQ,IAAI;AACxB,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI,kBAAkB,WAAY;AAChC,MAAI,aAAa,oBAAY,OAAO,WAAW,8BAA8B,IAAI;AACjF,MAAI,KAAK,WAAO,yBAAS,WAAY;AACjC,QAAI,mBAAW;AACb,cAAQ,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,WAAW,UAAU,OAAO,UAAU;AAAA,IACnH,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC,GAAG,CAAC,GACL,QAAQ,GAAG,CAAC,GACZ,WAAW,GAAG,CAAC;AACjB,gCAAU,WAAY;AACpB,QAAI,gBAAgB,SAAU,OAAO;AACnC,UAAI,MAAM,SAAS;AACjB,iBAAS,UAAU,IAAI;AAAA,MACzB,OAAO;AACL,iBAAS,UAAU,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,mBAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,iBAAiB,UAAU,aAAa;AAC3G,WAAO,WAAY;AACjB,qBAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,oBAAoB,UAAU,aAAa;AAAA,IAChH;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AACe,SAAR,SAA0B,SAAS;AACxC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,kBAAkB,QAAQ;AAC9B,MAAI,KAAK,WAAO,yBAAS,WAAY;AACjC,QAAI,sBAAsB,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,WAAW,aAAa,QAAQ,eAAe;AAC3J,WAAO,qBAAqB,qBAAqB,UAAU;AAAA,EAC7D,CAAC,GAAG,CAAC,GACL,YAAY,GAAG,CAAC,GAChB,eAAe,GAAG,CAAC;AACrB,MAAI,+BAA+B,SAAU,MAAM;AACjD,iBAAa,IAAI;AACjB,QAAI,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,QAAQ;AAC5F,mBAAa,QAAQ,iBAAiB,IAAI;AAAA,IAC5C;AAAA,EACF;AACA,MAAI,eAAe,gBAAgB;AACnC,MAAI,QAAQ,cAAc,UAAU,SAAS,eAAe;AAC5D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,cAAc,sBAAc,4BAA4B;AAAA,EAC1D;AACF;", "names": ["isObject", "debounce", "debounce", "isObject", "throttle", "isEqual", "document", "screenfull", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "_a", "import_react", "debounce", "_a", "import_react", "import_react", "listeners", "subscribe", "_a", "import_react", "listeners", "subscribe", "_a", "import_react", "import_react", "throttle", "_a", "import_react", "import_react", "<PERSON>tch", "subscribe", "initState", "initState", "_a", "useRequest_default", "useRequest_default", "_a", "_b", "_a", "_b", "_c", "import_react", "import_react", "import_react", "import_react", "import_react", "useEffectWithTarget", "import_react", "trigger", "converter", "import_react", "_a", "import_react", "dayjs", "_a", "import_react", "import_react", "import_debounce", "import_react", "debounce", "import_react", "import_react", "import_react", "isEqual", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "EventEmitter", "import_react", "import_react", "_a", "import_react", "import_react", "_a", "_b", "import_react", "screenfull", "_a", "_b", "import_react", "import_react", "_a", "import_react", "useRequest_default", "_a", "_b", "_c", "_d", "import_react", "document", "IntersectionObserver", "throttle", "observer", "win", "frame", "import_react", "observer", "_a", "import_react", "import_react", "_a", "_b", "eventName", "import_react", "import_react", "import_react", "_a", "_b", "import_react", "import_react", "_a", "import_react", "NetworkEventType", "import_react", "import_react", "import_react", "cancelAnimationFrameIsNotDefined", "import_react", "isPlainObject", "import_react", "import_react", "import_react", "import_react", "import_isPlainObject", "import_react", "isPlainObject", "import_react", "import_react", "import_react", "useEffectWithTarget", "_a", "import_react", "initState", "import_react", "import_throttle", "import_react", "throttle", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "import_react", "ReadyState", "_a", "import_react", "observer", "import_react", "ThemeMode"]}